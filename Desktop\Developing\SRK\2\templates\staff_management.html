<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Management - VishnoRex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">VishnoRex - Staff Management</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-label="Toggle navigation" title="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin_dashboard') }}">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="#">Staff Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('department_shifts') }}">Department Shifts</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ session.full_name }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- CSRF Token for AJAX requests -->
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Staff Management</h5>
                        <div>
                            <button class="btn btn-success btn-sm me-2" data-bs-toggle="modal" data-bs-target="#addStaffModal">
                                <i class="bi bi-person-plus"></i> Add Staff
                            </button>
                            <button class="btn btn-info btn-sm" id="exportStaffExcelBtn">
                                <i class="bi bi-file-earmark-excel"></i> Export to Excel
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>Staff Management:</strong> Manage all staff members and their details.
                            Shift types are automatically assigned based on department settings.
                            <a href="{{ url_for('department_shifts') }}" class="alert-link">Configure department shifts</a>
                        </div>

                        <!-- Search and Filter Section -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                                    <input type="text" class="form-control" id="staffSearchInput" placeholder="Search by name, staff ID, department...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="departmentFilter">
                                    <option value="">All Departments</option>
                                    <option value="Teaching">Teaching</option>
                                    <option value="Administration">Administration</option>
                                    <option value="Support">Support</option>
                                    <option value="Management">Management</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="genderFilter">
                                    <option value="">All Genders</option>
                                    <option value="Male">Male</option>
                                    <option value="Female">Female</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>

                        <!-- Staff Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="staffTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Staff ID</th>
                                        <th>First Name</th>
                                        <th>Last Name</th>
                                        <th>Date of Birth</th>
                                        <th>Date of Joining</th>
                                        <th>Department</th>
                                        <th>Destination</th>
                                        <th>Gender</th>
                                        <th>Phone Number</th>
                                        <th>Email ID</th>
                                        <th>Shift Assign Type</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="staffTableBody">
                                    {% for staff_member in staff %}
                                    <tr data-staff-id="{{ staff_member.id }}">
                                        <td>{{ staff_member.staff_id }}</td>
                                        <td>{{ staff_member.first_name or 'N/A' }}</td>
                                        <td>{{ staff_member.last_name or 'N/A' }}</td>
                                        <td>{{ staff_member.date_of_birth or 'N/A' }}</td>
                                        <td>{{ staff_member.date_of_joining or 'N/A' }}</td>
                                        <td>{{ staff_member.department or 'N/A' }}</td>
                                        <td>{{ staff_member.destination or 'N/A' }}</td>
                                        <td>{{ staff_member.gender or 'N/A' }}</td>
                                        <td>{{ staff_member.phone or 'N/A' }}</td>
                                        <td>{{ staff_member.email or 'N/A' }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ staff_member.shift_type or 'General' }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary edit-staff-btn" 
                                                        data-staff-id="{{ staff_member.id }}"
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#editStaffModal">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger delete-staff-btn" 
                                                        data-staff-id="{{ staff_member.id }}"
                                                        data-staff-name="{{ staff_member.full_name }}">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        {% if not staff %}
                        <div class="text-center py-4">
                            <i class="bi bi-people display-1 text-muted"></i>
                            <h4 class="text-muted">No Staff Members Found</h4>
                            <p class="text-muted">Click "Add Staff" to add your first staff member.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Staff Modal -->
    <div class="modal fade" id="addStaffModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">Add New Staff Member</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="addStaffForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="addStaffId" class="form-label">Staff ID *</label>
                                    <input type="text" class="form-control" id="addStaffId" name="staff_id" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="addFirstName" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="addFirstName" name="first_name" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="addLastName" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="addLastName" name="last_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="addDateOfBirth" class="form-label">Date of Birth</label>
                                    <input type="date" class="form-control" id="addDateOfBirth" name="date_of_birth">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="addDateOfJoining" class="form-label">Date of Joining</label>
                                    <input type="date" class="form-control" id="addDateOfJoining" name="date_of_joining">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="addDepartment" class="form-label">Department</label>
                                    <select class="form-select" id="addDepartment" name="department">
                                        <option value="">Select Department</option>
                                        <option value="Teaching">Teaching</option>
                                        <option value="Administration">Administration</option>
                                        <option value="Support">Support</option>
                                        <option value="Management">Management</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="addDestination" class="form-label">Destination *</label>
                                    <input type="text" class="form-control" id="addDestination" name="destination" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="addGender" class="form-label">Gender</label>
                                    <select class="form-select" id="addGender" name="gender">
                                        <option value="">Select Gender</option>
                                        <option value="Male">Male</option>
                                        <option value="Female">Female</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="addPhone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="addPhone" name="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="addEmail" class="form-label">Email ID</label>
                                    <input type="email" class="form-control" id="addEmail" name="email">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="addShiftType" class="form-label">Shift Assign Type</label>
                                    <select class="form-select" id="addShiftType" name="shift_type">
                                        <option value="general">General</option>
                                        <option value="morning">Morning</option>
                                        <option value="evening">Evening</option>
                                        <option value="night">Night</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="addPassword" class="form-label">Password *</label>
                                    <input type="password" class="form-control" id="addPassword" name="password" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Add Staff Member</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Staff Modal -->
    <div class="modal fade" id="editStaffModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">Edit Staff Member</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editStaffForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <input type="hidden" id="editStaffDbId" name="staff_db_id">
                    <div class="modal-body" id="editStaffModalBody">
                        <!-- Content will be loaded dynamically -->
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading staff details...</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Staff Member</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Pass department shift mappings to JavaScript
        window.deptShiftMap = {{ dept_shift_map | tojson }};
    </script>
    <script src="{{ url_for('static', filename='js/staff_management.js') }}"></script>
</body>
</html>
