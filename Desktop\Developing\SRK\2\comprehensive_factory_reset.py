#!/usr/bin/env python3
"""
Comprehensive Factory Reset Implementation
Multiple methods to factory reset ZK biometric device
"""

import socket
import struct
import time
import subprocess
import sys
import requests
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ZKFactoryReset:
    """Comprehensive factory reset implementation"""
    
    def __init__(self, current_ip="*************"):
        self.current_ip = current_ip
        self.possible_ips = [
            "*************",
            "*************", 
            "*************",
            "***********",
            "***********"
        ]
        self.possible_ports = [32150, 4370, 80, 8080]
        
    def method1_protocol_reset(self):
        """Method 1: Protocol-based factory reset"""
        print("🔄 METHOD 1: Protocol-based Factory Reset")
        print("-" * 50)
        
        reset_commands = [
            # Standard ZK reset commands
            b'\x50\x50\x82\x7d\x13\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00',  # Connect
            b'\x50\x50\x82\x7d\x14\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00',  # Factory reset
            b'\x50\x50\x82\x7d\x15\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00',  # Clear data
            b'\x50\x50\x82\x7d\x16\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00',  # Restart
        ]
        
        success = False
        
        for port in [32150, 4370, 80]:
            print(f"Trying protocol reset on port {port}...")
            
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                sock.connect((self.current_ip, port))
                
                for i, cmd in enumerate(reset_commands):
                    print(f"  Sending reset command {i+1}/4...")
                    sock.send(cmd)
                    time.sleep(2)
                    
                    try:
                        response = sock.recv(1024)
                        if response:
                            print(f"    Response: {len(response)} bytes")
                    except:
                        pass
                
                sock.close()
                print(f"✅ Reset commands sent on port {port}")
                success = True
                break
                
            except Exception as e:
                print(f"❌ Port {port} failed: {e}")
                continue
        
        if success:
            print("⏳ Waiting 60 seconds for device to reset...")
            time.sleep(60)
            return True
        
        return False
    
    def method2_http_reset(self):
        """Method 2: HTTP-based factory reset"""
        print("\n🌐 METHOD 2: HTTP-based Factory Reset")
        print("-" * 50)
        
        session = requests.Session()
        session.verify = False
        
        # Try to login and find reset option
        for ip in [self.current_ip]:
            for port in [80, 443, 8080]:
                try:
                    if port == 443:
                        url = f"https://{ip}"
                    else:
                        url = f"http://{ip}:{port}" if port != 80 else f"http://{ip}"
                    
                    print(f"Trying HTTP reset via {url}...")
                    
                    # Login
                    login_data = {
                        "username": "admin",
                        "password": "123456",
                        "user": "admin",
                        "pass": "123456"
                    }
                    
                    response = session.post(url, data=login_data, timeout=10)
                    
                    if response.status_code == 200:
                        print("  ✅ Logged in successfully")
                        
                        # Try common reset endpoints
                        reset_endpoints = [
                            "/factory_reset",
                            "/reset",
                            "/system/reset",
                            "/admin/reset",
                            "/cgi-bin/reset"
                        ]
                        
                        for endpoint in reset_endpoints:
                            try:
                                reset_url = url + endpoint
                                reset_response = session.post(reset_url, 
                                    data={"factory_reset": "1", "confirm": "yes"}, 
                                    timeout=5)
                                
                                if reset_response.status_code == 200:
                                    print(f"  ✅ Factory reset triggered via {endpoint}")
                                    time.sleep(30)
                                    return True
                                    
                            except:
                                continue
                        
                        print("  ⚠️ No reset endpoint found")
                    
                except Exception as e:
                    print(f"  ❌ Failed: {e}")
                    continue
        
        return False
    
    def method3_snmp_reset(self):
        """Method 3: SNMP-based reset (if supported)"""
        print("\n📊 METHOD 3: SNMP-based Reset")
        print("-" * 50)
        
        try:
            import socket
            
            # SNMP factory reset OID (device-specific)
            snmp_reset_packet = bytes.fromhex("30820100020103300f02020000020300ffe30401040201030410300e0400020100020100040004000400301e040004000400301004000400040004000400040004000400")
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(10)
            
            print("Sending SNMP reset command...")
            sock.sendto(snmp_reset_packet, (self.current_ip, 161))
            
            try:
                response, addr = sock.recvfrom(1024)
                print(f"✅ SNMP reset response received: {len(response)} bytes")
                time.sleep(30)
                return True
            except socket.timeout:
                print("⚠️ No SNMP response (may still have worked)")
                time.sleep(30)
                return True
                
        except Exception as e:
            print(f"❌ SNMP reset failed: {e}")
            return False
    
    def scan_for_reset_device(self):
        """Scan network for device after reset"""
        print("\n🔍 SCANNING FOR DEVICE AFTER RESET")
        print("-" * 50)
        
        found_devices = []
        
        for ip in self.possible_ips:
            for port in self.possible_ports:
                try:
                    print(f"Scanning {ip}:{port}...", end=" ")
                    
                    # Test socket connection
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(3)
                    result = sock.connect_ex((ip, port))
                    sock.close()
                    
                    if result == 0:
                        print("✅ OPEN")
                        
                        # Test ZK protocol
                        try:
                            from zk_biometric import ZKBiometricDevice
                            device = ZKBiometricDevice(
                                device_ip=ip,
                                port=port,
                                timeout=5,
                                device_id='1',  # Default after reset
                                use_cloud=False
                            )
                            
                            if device.connect():
                                print(f"    🎯 ZK protocol working!")
                                device.disconnect()
                                found_devices.append({
                                    'ip': ip,
                                    'port': port,
                                    'status': 'ZK_WORKING'
                                })
                            else:
                                found_devices.append({
                                    'ip': ip,
                                    'port': port,
                                    'status': 'OPEN_NO_ZK'
                                })
                        except:
                            found_devices.append({
                                'ip': ip,
                                'port': port,
                                'status': 'OPEN_NO_ZK'
                            })
                    else:
                        print("❌ Closed")
                        
                except Exception as e:
                    print(f"❌ Error")
        
        return found_devices
    
    def test_factory_defaults(self, found_devices):
        """Test factory default configurations"""
        print(f"\n🧪 TESTING FACTORY DEFAULT CONFIGURATIONS")
        print("-" * 50)
        
        if not found_devices:
            print("❌ No accessible devices found")
            return None
        
        for device in found_devices:
            ip = device['ip']
            port = device['port']
            
            print(f"Testing {ip}:{port}...")
            
            # Test with common factory defaults
            default_configs = [
                {'device_id': '1', 'common_key': '0'},
                {'device_id': '1', 'common_key': '1302'},
                {'device_id': '0', 'common_key': '0'},
            ]
            
            for config in default_configs:
                try:
                    from zk_biometric import ZKBiometricDevice
                    device_obj = ZKBiometricDevice(
                        device_ip=ip,
                        port=port,
                        timeout=10,
                        device_id=config['device_id'],
                        use_cloud=False
                    )
                    
                    if device_obj.connect():
                        print(f"✅ SUCCESS! Working configuration:")
                        print(f"   IP: {ip}")
                        print(f"   Port: {port}")
                        print(f"   Device ID: {config['device_id']}")
                        print(f"   Common Key: {config['common_key']}")
                        
                        try:
                            users = device_obj.get_users()
                            print(f"   Users: {len(users)}")
                        except:
                            pass
                        
                        device_obj.disconnect()
                        return {
                            'ip': ip,
                            'port': port,
                            'device_id': config['device_id'],
                            'common_key': config['common_key']
                        }
                        
                except Exception as e:
                    continue
        
        return None
    
    def provide_manual_instructions(self):
        """Provide manual reset instructions"""
        print(f"\n📋 MANUAL FACTORY RESET INSTRUCTIONS")
        print("=" * 60)
        
        print("If software methods don't work, try physical reset:")
        print()
        
        print("🔧 HARDWARE RESET BUTTON METHOD:")
        print("1. Locate small RESET button on device (usually recessed)")
        print("2. Power OFF the device completely")
        print("3. Hold RESET button down")
        print("4. While holding RESET, power ON the device")
        print("5. Keep holding RESET for 15-20 seconds")
        print("6. Release RESET button")
        print("7. Wait 2-3 minutes for complete restart")
        print()
        
        print("🔧 DEVICE MENU RESET (if device has display):")
        print("1. Press MENU key on device")
        print("2. Navigate: System → Factory Reset")
        print("3. Confirm reset operation")
        print("4. Wait for device to restart")
        print()
        
        print("⚠️ After manual reset, device defaults are usually:")
        print("   - IP: ************* or *************")
        print("   - Port: 4370")
        print("   - Device ID: 1")
        print("   - Common Key: 0")
        print("   - Admin Password: 123456")

def main():
    """Main factory reset implementation"""
    print("🔄 COMPREHENSIVE FACTORY RESET IMPLEMENTATION")
    print("=" * 70)
    print("Device: *************")
    print("=" * 70)
    
    print("\n⚠️ WARNING: Factory reset will:")
    print("   - Erase ALL user data and fingerprints")
    print("   - Reset ALL settings to factory defaults")
    print("   - Clear ALL attendance records")
    print("   - Reset network settings")
    
    response = input("\nContinue with factory reset? (y/N): ").lower()
    if response != 'y':
        print("❌ Factory reset cancelled")
        return
    
    reset_tool = ZKFactoryReset()
    
    # Try multiple reset methods
    methods = [
        ("Protocol-based Reset", reset_tool.method1_protocol_reset),
        ("HTTP-based Reset", reset_tool.method2_http_reset),
        ("SNMP-based Reset", reset_tool.method3_snmp_reset)
    ]
    
    reset_success = False
    for method_name, method_func in methods:
        print(f"\n{'='*70}")
        print(f"TRYING: {method_name}")
        print(f"{'='*70}")
        
        try:
            if method_func():
                print(f"✅ {method_name} completed")
                reset_success = True
                break
            else:
                print(f"❌ {method_name} failed")
        except Exception as e:
            print(f"❌ {method_name} error: {e}")
    
    # Scan for device after reset
    found_devices = reset_tool.scan_for_reset_device()
    
    if found_devices:
        print(f"\n✅ Found {len(found_devices)} accessible device(s)")
        
        # Test factory defaults
        working_config = reset_tool.test_factory_defaults(found_devices)
        
        if working_config:
            print(f"\n🎉 FACTORY RESET SUCCESSFUL!")
            print(f"Device is now accessible with factory defaults:")
            print(f"   IP: {working_config['ip']}")
            print(f"   Port: {working_config['port']}")
            print(f"   Device ID: {working_config['device_id']}")
            print(f"   Web Interface: http://{working_config['ip']}")
            
            print(f"\n📋 NEXT STEPS:")
            print("1. Access web interface to reconfigure device")
            print("2. Set your desired IP, Device ID, and Common Key")
            print("3. Test connection with your applications")
        else:
            print(f"\n⚠️ Device found but ZK protocol not working")
            print("Manual configuration may be needed")
    else:
        print(f"\n❌ No accessible devices found after reset")
        reset_tool.provide_manual_instructions()

if __name__ == '__main__':
    main()
