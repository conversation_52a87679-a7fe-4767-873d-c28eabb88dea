# ZK Biometric Device 181 Configuration
# Device-specific settings

# Flask Application Settings
SECRET_KEY=zk_device_181_secret_key_1302
FLASK_ENV=development
FLASK_DEBUG=true

# Device 181 Specific Settings
DEVICE_ID=181
DEVICE_COMMON_KEY=1302
DEVICE_NAME=ZK_Biometric_Device_181

# Cloud Service Configuration - Updated with your device settings
CLOUD_API_BASE_URL=http://18***********
CLOUD_WEBSOCKET_URL=ws://18***********
CLOUD_MQTT_BROKER=18***********
CLOUD_MQTT_PORT=4370

# Authentication (Update with your actual credentials)
CLOUD_API_KEY=device_181_api_key_1302
CLOUD_SECRET_KEY=device_181_secret_key_1302
CLOUD_ORG_ID=device_181_org

# Connection Settings for Device 181
CLOUD_USE_SSL=false
CLOUD_VERIFY_SSL=false
CLOUD_CONNECTION_TIMEOUT=30
CLOUD_RETRY_ATTEMPTS=5
CLOUD_HEARTBEAT_INTERVAL=60

# Sync Settings
CLOUD_AUTO_SYNC=true
CLOUD_SYNC_INTERVAL=30
CLOUD_BATCH_SIZE=100

# Device Configuration
DEFAULT_DEVICE_IP=************
DEFAULT_DEVICE_PORT=4370
DEFAULT_DEVICE_ID=181
DEFAULT_COMMON_KEY=1302
DEFAULT_DEVICE_TIMEOUT=10

# Network Configuration
NETWORK_INTERFACE=auto
NETWORK_SUBNET=*************
NETWORK_GATEWAY=***********

# Performance Settings
MAX_CONCURRENT_CONNECTIONS=50
REQUEST_TIMEOUT=30
MAX_RETRY_ATTEMPTS=5
QUEUE_MAX_SIZE=1000

# Feature Flags
ENABLE_CLOUD_FEATURES=true
ENABLE_WEBSOCKET=true
ENABLE_MQTT=false
ENABLE_API_RATE_LIMITING=true
ENABLE_REQUEST_LOGGING=true
ENABLE_PC_CONNECTION=true

# Development Settings
DEV_MOCK_DEVICES=false
DEV_SKIP_DEVICE_VALIDATION=false
DEV_ENABLE_DEBUG_ROUTES=true
