#!/usr/bin/env python3
"""
ZK Biometric Cloud Deployment Script
Automated setup and deployment for cloud-enabled ZK biometric system
"""

import os
import sys
import subprocess
import shutil
import json
import platform
from pathlib import Path

def print_header(title):
    """Print formatted header"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def print_step(step, description):
    """Print formatted step"""
    print(f"\n📋 Step {step}: {description}")
    print("-" * 40)

def run_command(command, description="", check=True):
    """Run shell command with error handling"""
    print(f"🔧 {description or command}")
    try:
        result = subprocess.run(command, shell=True, check=check, 
                              capture_output=True, text=True)
        if result.stdout:
            print(f"✅ {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stderr:
            print(f"   {e.stderr.strip()}")
        return False

def check_python_version():
    """Check Python version compatibility"""
    print_step(1, "Checking Python Version")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True

def install_dependencies():
    """Install required dependencies"""
    print_step(2, "Installing Dependencies")
    
    # Check if requirements.txt exists
    if not os.path.exists('requirements.txt'):
        print("❌ requirements.txt not found")
        return False
    
    # Install dependencies
    success = run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python packages"
    )
    
    if success:
        print("✅ All dependencies installed successfully")
    
    return success

def setup_environment():
    """Set up environment configuration"""
    print_step(3, "Setting Up Environment")
    
    # Check if .env already exists
    if os.path.exists('.env'):
        response = input("📝 .env file already exists. Overwrite? (y/N): ")
        if response.lower() != 'y':
            print("⏭️  Skipping environment setup")
            return True
    
    # Copy example environment file
    if os.path.exists('.env.example'):
        shutil.copy('.env.example', '.env')
        print("✅ Created .env file from template")
        print("📝 Please edit .env file with your actual configuration values")
        return True
    else:
        print("⚠️  .env.example not found, creating basic .env file")
        
        # Create basic .env file
        env_content = """# ZK Biometric Cloud Configuration
SECRET_KEY=change_this_in_production
FLASK_ENV=development

# Cloud Configuration (update these values)
CLOUD_API_BASE_URL=https://api.zkcloud.example.com
CLOUD_WEBSOCKET_URL=wss://ws.zkcloud.example.com
CLOUD_API_KEY=your_api_key_here
CLOUD_SECRET_KEY=your_secret_key_here
CLOUD_ORG_ID=your_organization_id

# Connection Settings
CLOUD_USE_SSL=true
CLOUD_AUTO_SYNC=true
CLOUD_SYNC_INTERVAL=30
"""
        
        with open('.env', 'w') as f:
            f.write(env_content)
        
        print("✅ Created basic .env file")
        print("📝 Please edit .env file with your actual configuration values")
        return True

def setup_database():
    """Set up database"""
    print_step(4, "Setting Up Database")
    
    try:
        # Import and initialize database
        from database import init_db
        from flask import Flask
        
        # Create minimal Flask app for database initialization
        app = Flask(__name__)
        app.config['SECRET_KEY'] = 'temp_key_for_setup'
        
        with app.app_context():
            init_db(app)
        
        print("✅ Database initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False

def test_cloud_modules():
    """Test cloud module imports"""
    print_step(5, "Testing Cloud Modules")
    
    modules = [
        'cloud_config',
        'cloud_connector',
        'cloud_api',
        'cloud_security'
    ]
    
    success_count = 0
    
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module} - OK")
            success_count += 1
        except ImportError as e:
            print(f"❌ {module} - Failed: {e}")
    
    if success_count == len(modules):
        print("✅ All cloud modules loaded successfully")
        return True
    else:
        print(f"⚠️  {success_count}/{len(modules)} cloud modules loaded")
        return False

def configure_cloud():
    """Configure cloud settings"""
    print_step(6, "Configuring Cloud Settings")
    
    try:
        from cloud_config import CloudConfigManager
        
        # Create configuration manager
        config_manager = CloudConfigManager()
        
        print("📝 Cloud configuration wizard")
        print("   (Press Enter to use default values)")
        
        # Get configuration from user
        api_url = input(f"API Base URL [{config_manager.config.api_base_url}]: ").strip()
        if api_url:
            config_manager.config.api_base_url = api_url
        
        websocket_url = input(f"WebSocket URL [{config_manager.config.websocket_url}]: ").strip()
        if websocket_url:
            config_manager.config.websocket_url = websocket_url
        
        api_key = input("API Key (required): ").strip()
        if api_key:
            config_manager.config.api_key = api_key
        
        org_id = input("Organization ID (required): ").strip()
        if org_id:
            config_manager.config.organization_id = org_id
        
        # Save configuration
        config_manager.save_config()
        
        print("✅ Cloud configuration saved")
        return True
        
    except ImportError:
        print("⚠️  Cloud modules not available, skipping cloud configuration")
        return True
    except Exception as e:
        print(f"❌ Cloud configuration failed: {e}")
        return False

def setup_device():
    """Set up device configuration"""
    print_step(7, "Setting Up Device Configuration")
    
    try:
        from cloud_config import CloudConfigManager, DeviceConfig
        
        config_manager = CloudConfigManager()
        
        print("📱 Device configuration wizard")
        
        device_id = input("Device ID [ZK_001]: ").strip() or "ZK_001"
        device_name = input("Device Name [Main Biometric Device]: ").strip() or "Main Biometric Device"
        local_ip = input("Device Local IP [*************]: ").strip() or "*************"
        
        cloud_enabled = input("Enable cloud connectivity? (Y/n): ").strip().lower()
        cloud_enabled = cloud_enabled != 'n'
        
        # Create device configuration
        device = DeviceConfig(
            device_id=device_id,
            device_name=device_name,
            local_ip=local_ip,
            cloud_enabled=cloud_enabled
        )
        
        # Add device
        if config_manager.add_device(device):
            print(f"✅ Device {device_id} configured successfully")
            return True
        else:
            print(f"⚠️  Device {device_id} already exists")
            return True
            
    except ImportError:
        print("⚠️  Cloud modules not available, skipping device configuration")
        return True
    except Exception as e:
        print(f"❌ Device configuration failed: {e}")
        return False

def run_tests():
    """Run system tests"""
    print_step(8, "Running System Tests")
    
    if os.path.exists('test_cloud_system.py'):
        success = run_command(
            f"{sys.executable} test_cloud_system.py",
            "Running test suite"
        )
        return success
    else:
        print("⚠️  Test file not found, skipping tests")
        return True

def create_startup_script():
    """Create startup script"""
    print_step(9, "Creating Startup Script")
    
    system = platform.system().lower()
    
    if system == 'windows':
        script_name = 'start_zk_biometric.bat'
        script_content = '''@echo off
echo Starting ZK Biometric Cloud System...
python app.py
pause
'''
    else:
        script_name = 'start_zk_biometric.sh'
        script_content = '''#!/bin/bash
echo "Starting ZK Biometric Cloud System..."
python3 app.py
'''
    
    with open(script_name, 'w') as f:
        f.write(script_content)
    
    if system != 'windows':
        os.chmod(script_name, 0o755)
    
    print(f"✅ Created startup script: {script_name}")
    return True

def print_summary():
    """Print deployment summary"""
    print_header("DEPLOYMENT COMPLETE")
    
    print("🎉 ZK Biometric Cloud System has been deployed successfully!")
    print("\n📋 Next Steps:")
    print("1. Edit .env file with your actual cloud service credentials")
    print("2. Review cloud_config.json for device settings")
    print("3. Start the application:")
    
    system = platform.system().lower()
    if system == 'windows':
        print("   - Double-click start_zk_biometric.bat")
        print("   - Or run: python app.py")
    else:
        print("   - Run: ./start_zk_biometric.sh")
        print("   - Or run: python3 app.py")
    
    print("4. Open http://localhost:5000 in your browser")
    print("5. Check cloud status in the admin dashboard")
    
    print("\n📚 Documentation:")
    print("- CLOUD_MIGRATION_GUIDE.md - Migration instructions")
    print("- README.md - Updated with cloud features")
    print("- cloud_example.py - Usage examples")
    
    print("\n🔧 Troubleshooting:")
    print("- Check logs in the console output")
    print("- Verify .env configuration")
    print("- Test device connectivity")
    print("- Run: python test_cloud_system.py")

def main():
    """Main deployment function"""
    print_header("ZK BIOMETRIC CLOUD DEPLOYMENT")
    print("This script will set up the cloud-enabled ZK biometric system")
    
    # Confirm deployment
    response = input("\n🚀 Start deployment? (Y/n): ")
    if response.lower() == 'n':
        print("❌ Deployment cancelled")
        return
    
    # Run deployment steps
    steps = [
        check_python_version,
        install_dependencies,
        setup_environment,
        setup_database,
        test_cloud_modules,
        configure_cloud,
        setup_device,
        run_tests,
        create_startup_script
    ]
    
    failed_steps = []
    
    for i, step in enumerate(steps, 1):
        try:
            if not step():
                failed_steps.append(step.__name__)
        except Exception as e:
            print(f"❌ Step {i} failed with error: {e}")
            failed_steps.append(step.__name__)
    
    # Print results
    if failed_steps:
        print_header("DEPLOYMENT COMPLETED WITH WARNINGS")
        print(f"⚠️  {len(failed_steps)} steps had issues:")
        for step in failed_steps:
            print(f"   - {step}")
        print("\n💡 The system may still work, but please review the warnings above")
    else:
        print_summary()

if __name__ == '__main__':
    main()
