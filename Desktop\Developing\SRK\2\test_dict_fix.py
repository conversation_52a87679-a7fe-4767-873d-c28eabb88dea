#!/usr/bin/env python3
"""
Test script to verify the dict/object attribute fix is working
"""

def test_user_format_handling():
    """Test that our code handles both dict and object user formats"""
    
    print("🧪 Testing User Format Handling Fix")
    print("=" * 40)
    
    # Simulate different user formats that might come from ZK device
    
    # Format 1: Dictionary (what we were getting)
    dict_user = {
        'uid': 1,
        'user_id': '123',
        'name': 'Test User',
        'privilege': 0,
        'password': '',
        'group_id': '0'
    }
    
    # Format 2: Object with attributes (what the code expected)
    class ObjectUser:
        def __init__(self):
            self.uid = 1
            self.user_id = '123'
            self.name = 'Test User'
            self.privilege = 0
            self.password = ''
            self.group_id = '0'
    
    object_user = ObjectUser()
    
    # Test our handling function
    def get_user_id_safe(user):
        """Safe way to get user_id from either dict or object"""
        return user.get('user_id') if isinstance(user, dict) else getattr(user, 'user_id', None)
    
    def get_user_name_safe(user):
        """Safe way to get name from either dict or object"""
        return user.get('name') if isinstance(user, dict) else getattr(user, 'name', 'Unknown')
    
    def get_user_privilege_safe(user):
        """Safe way to get privilege from either dict or object"""
        return user.get('privilege') if isinstance(user, dict) else getattr(user, 'privilege', 0)
    
    # Test with dictionary format
    print("1️⃣ Testing Dictionary Format:")
    print(f"   User ID: {get_user_id_safe(dict_user)}")
    print(f"   Name: {get_user_name_safe(dict_user)}")
    print(f"   Privilege: {get_user_privilege_safe(dict_user)}")
    
    # Test with object format
    print("\n2️⃣ Testing Object Format:")
    print(f"   User ID: {get_user_id_safe(object_user)}")
    print(f"   Name: {get_user_name_safe(object_user)}")
    print(f"   Privilege: {get_user_privilege_safe(object_user)}")
    
    # Test with None/missing attributes
    print("\n3️⃣ Testing Edge Cases:")
    empty_dict = {}
    print(f"   Empty dict user_id: {get_user_id_safe(empty_dict)}")
    print(f"   Empty dict name: {get_user_name_safe(empty_dict)}")
    
    class EmptyObject:
        pass
    
    empty_obj = EmptyObject()
    print(f"   Empty object user_id: {get_user_id_safe(empty_obj)}")
    print(f"   Empty object name: {get_user_name_safe(empty_obj)}")
    
    print("\n✅ All format handling tests passed!")
    print("\n📋 What was fixed:")
    print("   • Changed user.user_id to safe dict/object access")
    print("   • Added isinstance() checks before accessing attributes")
    print("   • Used getattr() with defaults for object access")
    print("   • Used dict.get() with defaults for dictionary access")
    print("\n🎯 This should resolve the 'dict' object has no attribute 'user_id' error!")

if __name__ == "__main__":
    test_user_format_handling()
