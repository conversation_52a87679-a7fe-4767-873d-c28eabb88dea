#!/usr/bin/env python3
"""
System Cleanup Verification Test Suite

This script tests that the VishnoRex system works correctly after removing
notification and regularization features.
"""

import sqlite3
import os
import sys
from datetime import datetime, date


class SystemCleanupTestSuite:
    """Test suite to verify system functionality after cleanup"""
    
    def __init__(self):
        self.test_results = []
    
    def log_test(self, test_name, passed, message=""):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'name': test_name,
            'passed': passed,
            'message': message
        })
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
    
    def test_database_integrity(self):
        """Test database integrity after cleanup"""
        print("\n=== Testing Database Integrity ===")
        
        try:
            conn = sqlite3.connect('vishnorex.db')
            cursor = conn.cursor()
            
            # Test that essential tables still exist
            essential_tables = [
                'schools', 'admins', 'staff', 'attendance', 
                'leave_applications', 'shift_definitions'
            ]
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in cursor.fetchall()]
            
            missing_tables = [table for table in essential_tables if table not in existing_tables]
            
            if not missing_tables:
                self.log_test("Database - Essential tables exist", True)
            else:
                self.log_test("Database - Essential tables exist", False, f"Missing: {missing_tables}")
            
            # Test that regularization tables are removed
            reg_tables = [table for table in existing_tables if 'regularization' in table.lower()]
            notif_tables = [table for table in existing_tables if 'notification' in table.lower()]
            
            if not reg_tables:
                self.log_test("Database - Regularization tables removed", True)
            else:
                self.log_test("Database - Regularization tables removed", False, f"Still exist: {reg_tables}")
            
            if not notif_tables:
                self.log_test("Database - Notification tables removed", True)
            else:
                self.log_test("Database - Notification tables removed", False, f"Still exist: {notif_tables}")
            
            # Test attendance table structure
            cursor.execute("PRAGMA table_info(attendance)")
            attendance_columns = [col[1] for col in cursor.fetchall()]
            
            reg_columns = [col for col in attendance_columns if 'regularization' in col.lower()]
            
            if not reg_columns:
                self.log_test("Database - Attendance regularization columns removed", True)
            else:
                self.log_test("Database - Attendance regularization columns removed", False, f"Still exist: {reg_columns}")
            
            # Test that essential attendance columns still exist
            essential_attendance_columns = [
                'id', 'staff_id', 'date', 'time_in', 'time_out', 
                'status', 'late_duration_minutes', 'early_departure_minutes'
            ]
            
            missing_attendance_columns = [col for col in essential_attendance_columns if col not in attendance_columns]
            
            if not missing_attendance_columns:
                self.log_test("Database - Essential attendance columns exist", True)
            else:
                self.log_test("Database - Essential attendance columns exist", False, f"Missing: {missing_attendance_columns}")
            
            conn.close()
            return True
            
        except Exception as e:
            self.log_test("Database integrity", False, str(e))
            return False
    
    def test_file_cleanup(self):
        """Test that files have been properly cleaned up"""
        print("\n=== Testing File Cleanup ===")
        
        # Test JavaScript files
        js_files_to_check = [
            'static/js/staff_dashboard.js',
            'static/js/admin_dashboard.js'
        ]
        
        for js_file in js_files_to_check:
            if os.path.exists(js_file):
                try:
                    with open(js_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for regularization references
                    reg_references = content.lower().count('regularization')
                    notif_references = content.lower().count('notification')
                    
                    if reg_references == 0:
                        self.log_test(f"JavaScript - {js_file} regularization references removed", True)
                    else:
                        self.log_test(f"JavaScript - {js_file} regularization references removed", False, f"Found {reg_references} references")
                    
                    if js_file == 'static/js/staff_dashboard.js':
                        if notif_references == 0:
                            self.log_test(f"JavaScript - {js_file} notification references removed", True)
                        else:
                            self.log_test(f"JavaScript - {js_file} notification references removed", False, f"Found {notif_references} references")
                
                except Exception as e:
                    self.log_test(f"JavaScript file check - {js_file}", False, str(e))
            else:
                self.log_test(f"JavaScript file exists - {js_file}", False, "File not found")
        
        # Test HTML templates
        html_files_to_check = [
            'templates/staff_dashboard.html',
            'templates/admin_dashboard.html'
        ]
        
        for html_file in html_files_to_check:
            if os.path.exists(html_file):
                try:
                    with open(html_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for regularization and notification references
                    reg_references = content.lower().count('regularization')
                    notif_references = content.lower().count('notification')
                    
                    if reg_references == 0:
                        self.log_test(f"HTML - {html_file} regularization references removed", True)
                    else:
                        self.log_test(f"HTML - {html_file} regularization references removed", False, f"Found {reg_references} references")
                    
                    if html_file == 'templates/staff_dashboard.html':
                        if notif_references == 0:
                            self.log_test(f"HTML - {html_file} notification references removed", True)
                        else:
                            self.log_test(f"HTML - {html_file} notification references removed", False, f"Found {notif_references} references")
                
                except Exception as e:
                    self.log_test(f"HTML file check - {html_file}", False, str(e))
            else:
                self.log_test(f"HTML file exists - {html_file}", False, "File not found")
    
    def test_app_py_cleanup(self):
        """Test that app.py has been properly cleaned up"""
        print("\n=== Testing app.py Cleanup ===")
        
        if os.path.exists('app.py'):
            try:
                with open('app.py', 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for removed imports
                if 'AttendanceRegularizationManager' not in content:
                    self.log_test("app.py - AttendanceRegularizationManager import removed", True)
                else:
                    self.log_test("app.py - AttendanceRegularizationManager import removed", False, "Still found in code")
                
                if 'NotificationManager' not in content:
                    self.log_test("app.py - NotificationManager import removed", True)
                else:
                    self.log_test("app.py - NotificationManager import removed", False, "Still found in code")
                
                # Check for removed endpoints
                removed_endpoints = [
                    '/get_regularization_requests',
                    '/process_regularization_request',
                    '/get_staff_regularization_history',
                    '/get_staff_notifications',
                    '/mark_notification_read'
                ]
                
                for endpoint in removed_endpoints:
                    if endpoint not in content:
                        self.log_test(f"app.py - {endpoint} endpoint removed", True)
                    else:
                        self.log_test(f"app.py - {endpoint} endpoint removed", False, "Still found in code")
                
                # Check that essential functionality remains
                essential_endpoints = [
                    '/staff/dashboard',
                    '/admin/dashboard',
                    'check_device_verification',
                    '/get_weekly_attendance'
                ]
                
                for endpoint in essential_endpoints:
                    if endpoint in content:
                        self.log_test(f"app.py - {endpoint} endpoint preserved", True)
                    else:
                        self.log_test(f"app.py - {endpoint} endpoint preserved", False, "Not found in code")
                
            except Exception as e:
                self.log_test("app.py cleanup check", False, str(e))
        else:
            self.log_test("app.py file exists", False, "File not found")
    
    def test_backup_created(self):
        """Test that database backup was created"""
        print("\n=== Testing Backup Creation ===")
        
        # Look for backup files
        backup_files = [f for f in os.listdir('.') if f.startswith('vishnorex_backup_') and f.endswith('.db')]
        
        if backup_files:
            self.log_test("Database backup created", True, f"Found backup: {backup_files[-1]}")
        else:
            self.log_test("Database backup created", False, "No backup files found")
    
    def run_all_tests(self):
        """Run all cleanup verification tests"""
        print("🧹 Starting System Cleanup Verification Tests")
        print("=" * 60)
        
        self.test_database_integrity()
        self.test_file_cleanup()
        self.test_app_py_cleanup()
        self.test_backup_created()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 CLEANUP VERIFICATION SUMMARY")
        print("=" * 60)
        
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        total_tests = len(self.test_results)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        # Show failed tests
        failed_tests = [result for result in self.test_results if not result['passed']]
        if failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in failed_tests:
                print(f"  - {test['name']}: {test['message']}")
        
        return passed_tests == total_tests


def main():
    """Main function"""
    print("🔍 VishnoRex System Cleanup Verification")
    print("=" * 60)
    print("Verifying that notification and regularization features have been completely removed")
    print()
    
    test_suite = SystemCleanupTestSuite()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n🎉 All cleanup verification tests passed!")
        print("✅ The system has been successfully cleaned up")
        print("🚀 VishnoRex is ready to run without notification and regularization features")
        return True
    else:
        print("\n⚠️  Some verification tests failed")
        print("📝 Please review the failed tests and complete the cleanup")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
