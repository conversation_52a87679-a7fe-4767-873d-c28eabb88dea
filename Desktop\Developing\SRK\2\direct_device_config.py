#!/usr/bin/env python3
"""
Direct ZK Device Configuration Tool
Configure ZK device settings without web interface using direct protocol commands
"""

import socket
import struct
import time
import sys

class ZKDirectConfig:
    """Direct ZK device configuration using raw protocol"""
    
    def __init__(self, ip_address="*************", port=32150):
        self.ip_address = ip_address
        self.port = port
        self.socket = None
        self.session_id = 0
        self.reply_id = 0
        
    def connect(self):
        """Establish connection to ZK device"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((self.ip_address, self.port))
            
            # Send connection command
            command = self._create_command(1000, b'')  # CMD_CONNECT
            self.socket.send(command)
            
            response = self.socket.recv(1024)
            if len(response) >= 8:
                self.session_id = struct.unpack('<H', response[4:6])[0]
                print(f"✅ Connected to device (Session ID: {self.session_id})")
                return True
            else:
                print("❌ Invalid response from device")
                return False
                
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from device"""
        if self.socket:
            try:
                # Send disconnect command
                command = self._create_command(1001, b'')  # CMD_EXIT
                self.socket.send(command)
                self.socket.close()
                print("✅ Disconnected from device")
            except:
                pass
    
    def _create_command(self, command_id, data=b''):
        """Create ZK protocol command packet"""
        self.reply_id += 1
        
        # ZK protocol header
        header = struct.pack('<HHHH', 
                           0x5050,  # Start marker
                           command_id,  # Command ID
                           0,  # Checksum (will be calculated)
                           self.session_id)  # Session ID
        
        # Add reply ID and data length
        packet = header + struct.pack('<HH', self.reply_id, len(data)) + data
        
        # Calculate checksum
        checksum = sum(packet[8:]) % 65536
        packet = packet[:4] + struct.pack('<H', checksum) + packet[6:]
        
        return packet
    
    def send_command(self, command_id, data=b''):
        """Send command and get response"""
        try:
            command = self._create_command(command_id, data)
            self.socket.send(command)
            
            response = self.socket.recv(1024)
            if len(response) >= 8:
                return response[8:]  # Return data part
            return None
            
        except Exception as e:
            print(f"❌ Command failed: {e}")
            return None
    
    def enable_device(self):
        """Enable device for communication"""
        print("🔧 Enabling device...")
        response = self.send_command(1001, b'')  # CMD_ENABLEDEVICE
        if response is not None:
            print("✅ Device enabled")
            return True
        return False
    
    def disable_device(self):
        """Disable device temporarily"""
        print("🔧 Disabling device...")
        response = self.send_command(1002, b'')  # CMD_DISABLEDEVICE
        if response is not None:
            print("✅ Device disabled")
            return True
        return False
    
    def restart_device(self):
        """Restart the device"""
        print("🔄 Restarting device...")
        response = self.send_command(1004, b'')  # CMD_RESTART
        if response is not None:
            print("✅ Device restart command sent")
            return True
        return False
    
    def get_device_info(self):
        """Get device information"""
        print("📱 Getting device information...")
        
        # Try to get firmware version
        response = self.send_command(1100, b'~ZKFPVersion')
        if response:
            version = response.decode('utf-8', errors='ignore').strip('\x00')
            print(f"   Firmware: {version}")
        
        # Try to get device name
        response = self.send_command(1100, b'~DeviceName')
        if response:
            name = response.decode('utf-8', errors='ignore').strip('\x00')
            print(f"   Device Name: {name}")
        
        return True

def try_alternative_ports():
    """Try connecting on different common ZK ports"""
    ports = [32150, 4370, 8080, 80, 443]
    
    print("🔍 Scanning for ZK device on different ports...")
    print("-" * 50)
    
    for port in ports:
        print(f"Testing port {port}...", end=" ")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex(("*************", port))
            sock.close()
            
            if result == 0:
                print("✅ OPEN")
                
                # Try ZK protocol on this port
                config = ZKDirectConfig("*************", port)
                if config.connect():
                    print(f"   🎯 ZK protocol working on port {port}")
                    config.get_device_info()
                    config.disconnect()
                    return port
                else:
                    print(f"   ⚠️ Port open but not ZK protocol")
            else:
                print("❌ CLOSED")
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
    
    return None

def try_default_credentials():
    """Try connecting with different default settings"""
    print("\n🔑 Trying different device configurations...")
    print("-" * 50)
    
    # Common ZK device configurations
    configs = [
        {"device_id": "181", "common_key": "1302", "port": 32150},
        {"device_id": "1", "common_key": "0", "port": 32150},
        {"device_id": "1", "common_key": "1302", "port": 32150},
        {"device_id": "181", "common_key": "0", "port": 32150},
        {"device_id": "1", "common_key": "1302", "port": 4370},
    ]
    
    for i, config in enumerate(configs, 1):
        print(f"\n🔧 Configuration {i}:")
        print(f"   Device ID: {config['device_id']}")
        print(f"   Common Key: {config['common_key']}")
        print(f"   Port: {config['port']}")
        
        try:
            from zk_biometric import ZKBiometricDevice
            
            device = ZKBiometricDevice(
                device_ip='*************',
                port=config['port'],
                timeout=10,
                device_id=config['device_id'],
                use_cloud=False
            )
            
            print("   Testing connection...", end=" ")
            if device.connect():
                print("✅ SUCCESS!")
                try:
                    users = device.get_users()
                    print(f"   📱 Found {len(users)} users")
                    device.disconnect()
                    return config
                except Exception as e:
                    print(f"   ⚠️ Connected but limited access: {e}")
                    device.disconnect()
                    return config
            else:
                print("❌ Failed")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return None

def main():
    """Main configuration function"""
    print("🔧 ZK DEVICE DIRECT CONFIGURATION TOOL")
    print("=" * 60)
    print("Device: *************")
    print("Attempting configuration without web interface")
    print("=" * 60)
    
    # Step 1: Find working port
    print("\n📡 Step 1: Finding device communication port...")
    working_port = try_alternative_ports()
    
    if working_port:
        print(f"\n✅ Found working port: {working_port}")
    else:
        print("\n❌ No working ZK protocol port found")
        print("Device may need physical reset or different approach")
        return
    
    # Step 2: Try different configurations
    print(f"\n🔑 Step 2: Testing device configurations...")
    working_config = try_default_credentials()
    
    if working_config:
        print(f"\n🎉 SUCCESS! Working configuration found:")
        print(f"   Device ID: {working_config['device_id']}")
        print(f"   Common Key: {working_config['common_key']}")
        print(f"   Port: {working_config['port']}")
        
        print(f"\n📋 Use this configuration in your applications:")
        print(f"   device = ZKBiometricDevice(")
        print(f"       device_ip='*************',")
        print(f"       port={working_config['port']},")
        print(f"       device_id='{working_config['device_id']}',")
        print(f"       use_cloud=False")
        print(f"   )")
        
    else:
        print(f"\n⚠️ No working configuration found with standard methods")
        print(f"Device may need:")
        print(f"   - Physical reset to factory defaults")
        print(f"   - Different communication protocol")
        print(f"   - Hardware-level configuration")

if __name__ == '__main__':
    main()
