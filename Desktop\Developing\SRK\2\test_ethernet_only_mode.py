#!/usr/bin/env python3
"""
Test Ethernet-Only Mode
Verify that cloud functionality is disabled and Ethernet works
"""

import os
import sys

# Set environment variable to disable cloud
os.environ['DISABLE_CLOUD'] = '1'
os.environ['CLOUD_ENABLED'] = 'False'

def test_cloud_disabled():
    """Test that cloud functionality is disabled"""
    print("🧪 Testing Cloud Disable Status")
    print("-" * 40)
    
    try:
        from cloud_connector import CloudConnector, CLOUD_DISABLED
        
        print(f"CLOUD_DISABLED flag: {CLOUD_DISABLED}")
        
        if CLOUD_DISABLED:
            print("✅ Cloud functionality is disabled")
            
            # Test CloudConnector initialization
            connector = CloudConnector()
            if hasattr(connector, 'disabled') and connector.disabled:
                print("✅ CloudConnector is properly disabled")
                return True
            else:
                print("❌ CloudConnector not properly disabled")
                return False
        else:
            print("❌ Cloud functionality is still enabled")
            return False
            
    except Exception as e:
        print(f"❌ Error testing cloud disable: {e}")
        return False

def test_ethernet_connection():
    """Test direct Ethernet connection"""
    print("\n🔌 Testing Ethernet Connection")
    print("-" * 40)
    
    try:
        from zk_biometric import ZKBiometricDevice
        
        # Test device creation (should work without cloud)
        device = ZKBiometricDevice(
            device_ip='*************',
            port=32150,
            timeout=10,
            device_id='181',
            use_cloud=False
        )
        
        print("✅ ZKBiometricDevice created successfully")
        print(f"   Device IP: {device.device_ip}")
        print(f"   Port: {device.port}")
        print(f"   Device ID: {device.device_id}")
        print(f"   Use Cloud: {device.use_cloud}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Ethernet connection: {e}")
        return False

def test_config_loading():
    """Test configuration loading"""
    print("\n📋 Testing Configuration Loading")
    print("-" * 40)
    
    try:
        import json
        
        # Test cloud config loading
        with open('cloud_config.json', 'r') as f:
            config = json.load(f)
        
        cloud_config = config.get('config', {})
        
        print("Configuration status:")
        print(f"   Cloud Provider: {cloud_config.get('cloud_provider', 'unknown')}")
        print(f"   WebSocket URL: {cloud_config.get('websocket_url', 'none')}")
        print(f"   Auto Sync: {cloud_config.get('auto_sync', 'unknown')}")
        
        # Check if properly disabled
        if cloud_config.get('cloud_provider') == 'disabled':
            print("✅ Configuration shows cloud disabled")
            return True
        else:
            print("⚠️ Configuration may still have cloud enabled")
            return False
            
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return False

def test_device_accessibility():
    """Test device network accessibility"""
    print("\n🌐 Testing Device Network Accessibility")
    print("-" * 40)
    
    import socket
    
    devices = [
        {"ip": "*************", "port": 32150, "name": "Primary Device"},
        {"ip": "*************", "port": 80, "name": "Web Interface"},
        {"ip": "*************", "port": 4370, "name": "Factory Default"}
    ]
    
    accessible_count = 0
    
    for device in devices:
        try:
            print(f"Testing {device['name']} ({device['ip']}:{device['port']})...", end=" ")
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((device['ip'], device['port']))
            sock.close()
            
            if result == 0:
                print("✅ ACCESSIBLE")
                accessible_count += 1
            else:
                print("❌ Not accessible")
                
        except Exception as e:
            print(f"❌ Error")
    
    print(f"\nAccessible devices: {accessible_count}/{len(devices)}")
    return accessible_count > 0

def provide_next_steps():
    """Provide next steps for configuration"""
    print("\n📋 NEXT STEPS FOR DEVICE CONFIGURATION")
    print("=" * 60)
    
    print("1. 🛑 STOP your current Flask application if running")
    print("   - Press Ctrl+C in the terminal running the app")
    print()
    
    print("2. 🔌 START in Ethernet-only mode:")
    print("   - Run: python start_ethernet_only.py")
    print("   - Or: python app.py (with updated config)")
    print()
    
    print("3. 🌐 Configure device via web interface:")
    print("   - Open browser: http://*************")
    print("   - Login: admin / 123456")
    print("   - Find Communication/Network settings")
    print("   - Configure:")
    print("     * Device ID: 181")
    print("     * Common Key: 1302")
    print("     * Port: 32150")
    print("     * TCP/IP: Enable")
    print("   - Save and restart device")
    print()
    
    print("4. 🧪 Test connection:")
    print("   - Run: python simple_test_181.py")
    print("   - Should connect without WebSocket errors")

def main():
    """Main test function"""
    print("🔧 ETHERNET-ONLY MODE TEST")
    print("=" * 50)
    print("Testing cloud disable and Ethernet functionality")
    print("=" * 50)
    
    tests = [
        ("Cloud Disabled", test_cloud_disabled),
        ("Ethernet Connection", test_ethernet_connection),
        ("Configuration Loading", test_config_loading),
        ("Device Accessibility", test_device_accessibility)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed_tests += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n📊 TEST RESULTS: {passed_tests}/{len(tests)} tests passed")
    
    if passed_tests >= 3:
        print("🎉 Ethernet-only mode is working correctly!")
        print("✅ Cloud functionality disabled")
        print("✅ Ready for device configuration")
    else:
        print("⚠️ Some issues detected - check configuration")
    
    provide_next_steps()

if __name__ == '__main__':
    main()
