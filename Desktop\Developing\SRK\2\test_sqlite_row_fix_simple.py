#!/usr/bin/env python3
"""
Simple test to verify the sqlite3.Row fix is working
"""

import sqlite3
import datetime
import sys
import os

# Add current directory to path to import app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_calculate_daily_attendance_data():
    """Test the calculate_daily_attendance_data function with on-duty records"""
    print("=== Testing calculate_daily_attendance_data Function ===")
    
    # Import the function from app
    try:
        from app import calculate_daily_attendance_data
        print("✅ Successfully imported calculate_daily_attendance_data function")
    except ImportError as e:
        print(f"❌ Failed to import function: {e}")
        return False
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get an on-duty attendance record
        cursor.execute('''
            SELECT date, time_in, time_out, overtime_in, overtime_out, status,
                   late_duration_minutes, early_departure_minutes,
                   on_duty_type, on_duty_location, on_duty_purpose
            FROM attendance
            WHERE status = 'on_duty'
            LIMIT 1
        ''')
        
        attendance_record = cursor.fetchone()
        
        if not attendance_record:
            print("⚠️  No on-duty records found, creating test record...")
            
            # Get a staff member
            cursor.execute('SELECT id FROM staff LIMIT 1')
            staff = cursor.fetchone()
            
            if not staff:
                print("❌ No staff members found")
                return False
            
            staff_id = staff['id']
            test_date = '2025-07-28'
            
            # Create test on-duty record
            cursor.execute('''
                INSERT OR REPLACE INTO attendance 
                (staff_id, school_id, date, status, on_duty_type, on_duty_location, on_duty_purpose)
                VALUES (?, 1, ?, 'on_duty', 'Testing', 'Test Location', 'Testing sqlite3.Row fix')
            ''', (staff_id, test_date))
            
            conn.commit()
            
            # Fetch the created record
            cursor.execute('''
                SELECT date, time_in, time_out, overtime_in, overtime_out, status,
                       late_duration_minutes, early_departure_minutes,
                       on_duty_type, on_duty_location, on_duty_purpose
                FROM attendance
                WHERE staff_id = ? AND date = ?
            ''', (staff_id, test_date))
            
            attendance_record = cursor.fetchone()
        
        if not attendance_record:
            print("❌ Failed to get attendance record")
            return False
        
        print(f"✅ Found attendance record for {attendance_record['date']}")
        print(f"  - Status: {attendance_record['status']}")
        print(f"  - On-duty type: {attendance_record['on_duty_type']}")
        print(f"  - On-duty location: {attendance_record['on_duty_location']}")
        print(f"  - On-duty purpose: {attendance_record['on_duty_purpose']}")
        
        # Create a mock shift definition
        shift_def = {
            'start_time': '09:20:00',
            'end_time': '16:30:00',
            'grace_period_minutes': 10
        }
        
        # Test the function
        print("\nTesting calculate_daily_attendance_data function...")
        
        try:
            day_data = calculate_daily_attendance_data(
                datetime.date.today(),
                attendance_record,
                shift_def,
                'general'
            )
            
            print("✅ Function executed successfully!")
            print("Day data result:")
            for key, value in day_data.items():
                print(f"  - {key}: {value}")
            
            # Verify on-duty data is present
            if day_data.get('present_status') == 'On Duty':
                print("✅ On-duty status correctly detected")
                if 'on_duty_type' in day_data and day_data['on_duty_type']:
                    print("✅ On-duty type correctly retrieved")
                if 'on_duty_location' in day_data and day_data['on_duty_location']:
                    print("✅ On-duty location correctly retrieved")
                if 'on_duty_purpose' in day_data and day_data['on_duty_purpose']:
                    print("✅ On-duty purpose correctly retrieved")
                return True
            else:
                print(f"❌ Expected 'On Duty' status, got: {day_data.get('present_status')}")
                return False
                
        except Exception as e:
            print(f"❌ Function execution failed: {e}")
            print(f"Error type: {type(e).__name__}")
            return False
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False
    finally:
        conn.close()

def test_weekly_attendance_route():
    """Test that the weekly attendance route works without errors"""
    print("\n=== Testing Weekly Attendance Route Logic ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get staff with on-duty record
        cursor.execute('''
            SELECT DISTINCT s.id, s.full_name
            FROM staff s
            INNER JOIN attendance a ON s.id = a.staff_id
            WHERE a.status = 'on_duty'
            LIMIT 1
        ''')
        staff = cursor.fetchone()
        
        if not staff:
            print("❌ No staff with on-duty records found")
            return False
        
        staff_id = staff['id']
        staff_name = staff['full_name']
        print(f"Testing with staff: {staff_name} (ID: {staff_id})")
        
        # Simulate the weekly attendance query
        week_start = datetime.date(2025, 7, 21)
        week_end = week_start + datetime.timedelta(days=6)
        
        cursor.execute('''
            SELECT date, time_in, time_out, overtime_in, overtime_out, status,
                   late_duration_minutes, early_departure_minutes,
                   on_duty_type, on_duty_location, on_duty_purpose
            FROM attendance
            WHERE staff_id = ? AND date BETWEEN ? AND ?
            ORDER BY date
        ''', (staff_id, week_start, week_end))
        
        attendance_records = cursor.fetchall()
        
        print(f"✅ Found {len(attendance_records)} attendance records for the week")
        
        # Test accessing fields on each record
        for record in attendance_records:
            try:
                status = record['status'] if record['status'] else 'absent'
                print(f"  - {record['date']}: {status}")
                
                if status == 'on_duty':
                    on_duty_type = record['on_duty_type'] if record['on_duty_type'] else 'Official Work'
                    on_duty_location = record['on_duty_location'] if record['on_duty_location'] else 'Not specified'
                    on_duty_purpose = record['on_duty_purpose'] if record['on_duty_purpose'] else 'Official duty'
                    
                    print(f"    Type: {on_duty_type}")
                    print(f"    Location: {on_duty_location}")
                    print(f"    Purpose: {on_duty_purpose[:50]}...")
                    
            except Exception as e:
                print(f"❌ Error accessing record fields: {e}")
                return False
        
        print("✅ All record field access successful")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("🔧 Testing SQLite Row Fix")
    print("=" * 50)
    
    results = []
    
    # Run tests
    results.append(("calculate_daily_attendance_data Function", test_calculate_daily_attendance_data()))
    results.append(("Weekly Attendance Route Logic", test_weekly_attendance_route()))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 SQLITE ROW FIX SUCCESSFUL!")
        print("\nThe sqlite3.Row error has been resolved:")
        print("1. ✅ No more .get() method calls on Row objects")
        print("2. ✅ Proper null-safe field access implemented")
        print("3. ✅ On-duty functionality working correctly")
        print("4. ✅ Weekly calendar data processing fixed")
        
        print("\n🚀 On-duty attendance integration is now error-free!")
    else:
        print(f"\n⚠️  {total - passed} issues still need attention")
        print("Please check the failed tests above.")
