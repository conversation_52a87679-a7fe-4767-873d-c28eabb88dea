#!/usr/bin/env python3
"""
Automated ZK Device Configuration
Automatically configure the device via HTTP interface
"""

import requests
import re
import time
from urllib.parse import urljoin, parse_qs, urlparse
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class AutoZKConfig:
    """Automated ZK device configuration"""
    
    def __init__(self, ip="*************"):
        self.ip = ip
        self.base_url = f"http://{ip}"
        self.session = requests.Session()
        self.session.verify = False
        
    def login(self, username="admin", password="123456"):
        """Login to device"""
        print(f"🔐 Logging into device...")
        
        try:
            # Get login page
            response = self.session.get(self.base_url, timeout=10)
            
            if response.status_code != 200:
                print(f"❌ Cannot access device: HTTP {response.status_code}")
                return False
            
            # Try to find login form
            login_data = {
                "username": username,
                "password": password,
                "user": username,
                "pass": password,
                "login": "Login",
                "submit": "Login"
            }
            
            # Post login data
            login_response = self.session.post(self.base_url, data=login_data, timeout=10)
            
            if login_response.status_code == 200:
                content = login_response.text.lower()
                
                if any(success in content for success in ['main', 'system', 'device', 'dashboard', 'menu']):
                    print(f"✅ Successfully logged in as {username}")
                    return True
                else:
                    print(f"❌ Login failed - invalid credentials")
                    return False
            else:
                print(f"❌ Login failed: HTTP {login_response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False
    
    def find_communication_settings(self):
        """Find communication settings page"""
        print(f"🔍 Looking for communication settings...")
        
        # Common paths for communication settings
        comm_paths = [
            "/comm.html",
            "/communication.html",
            "/system/comm.html",
            "/system/communication.html",
            "/network.html",
            "/tcpip.html",
            "/settings/comm.html",
            "/config/comm.html",
            "/admin/comm.html"
        ]
        
        for path in comm_paths:
            try:
                url = urljoin(self.base_url, path)
                response = self.session.get(url, timeout=5)
                
                if response.status_code == 200:
                    content = response.text.lower()
                    
                    if any(keyword in content for keyword in ['tcp', 'communication', 'device id', 'common key', 'port']):
                        print(f"✅ Found communication settings at: {path}")
                        return url, response.text
                        
            except Exception as e:
                continue
        
        print(f"⚠️ Communication settings page not found")
        return None, None
    
    def parse_form_fields(self, html_content):
        """Parse form fields from HTML"""
        fields = {}
        
        # Find input fields
        input_pattern = r'<input[^>]*name=["\']([^"\']*)["\'][^>]*(?:value=["\']([^"\']*)["\'])?[^>]*>'
        inputs = re.findall(input_pattern, html_content, re.IGNORECASE)
        
        for name, value in inputs:
            fields[name] = value
        
        # Find select fields
        select_pattern = r'<select[^>]*name=["\']([^"\']*)["\'][^>]*>(.*?)</select>'
        selects = re.findall(select_pattern, html_content, re.IGNORECASE | re.DOTALL)
        
        for name, options in selects:
            # Find selected option
            selected_pattern = r'<option[^>]*selected[^>]*value=["\']([^"\']*)["\']'
            selected = re.search(selected_pattern, options, re.IGNORECASE)
            if selected:
                fields[name] = selected.group(1)
            else:
                # Find first option value
                option_pattern = r'<option[^>]*value=["\']([^"\']*)["\']'
                option = re.search(option_pattern, options, re.IGNORECASE)
                if option:
                    fields[name] = option.group(1)
        
        return fields
    
    def configure_communication(self):
        """Configure communication settings"""
        print(f"🔧 Configuring communication settings...")
        
        # Find communication settings page
        comm_url, comm_html = self.find_communication_settings()
        
        if not comm_url:
            print(f"❌ Cannot find communication settings page")
            return False
        
        # Parse existing form fields
        fields = self.parse_form_fields(comm_html)
        print(f"📋 Found {len(fields)} form fields")
        
        # Update with our desired settings
        target_settings = {
            'device_id': '181',
            'deviceid': '181',
            'id': '181',
            'common_key': '1302',
            'commonkey': '1302',
            'key': '1302',
            'port': '32150',
            'tcp_port': '32150',
            'tcpport': '32150',
            'tcp_enable': '1',
            'tcpenable': '1',
            'tcp_enabled': '1',
            'enable_tcp': '1',
            'comm_mode': '1',
            'communication': '1',
            'mode': '1'
        }
        
        # Update fields with target settings
        updated = False
        for key, value in target_settings.items():
            if key in fields:
                old_value = fields[key]
                fields[key] = value
                if old_value != value:
                    print(f"   📝 {key}: {old_value} → {value}")
                    updated = True
        
        if not updated:
            print(f"⚠️ No matching fields found to update")
            print(f"Available fields: {list(fields.keys())}")
        
        # Submit the form
        try:
            response = self.session.post(comm_url, data=fields, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ Configuration submitted successfully")
                return True
            else:
                print(f"❌ Configuration failed: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Configuration error: {e}")
            return False
    
    def restart_device(self):
        """Restart the device"""
        print(f"🔄 Attempting to restart device...")
        
        restart_paths = [
            "/restart.html",
            "/reboot.html",
            "/system/restart.html",
            "/admin/restart.html"
        ]
        
        for path in restart_paths:
            try:
                url = urljoin(self.base_url, path)
                response = self.session.get(url, timeout=5)
                
                if response.status_code == 200:
                    # Try to submit restart form
                    restart_data = {"restart": "1", "reboot": "1", "submit": "Restart"}
                    self.session.post(url, data=restart_data, timeout=5)
                    print(f"✅ Restart command sent")
                    return True
                    
            except Exception as e:
                continue
        
        print(f"⚠️ Could not find restart option")
        return False
    
    def verify_configuration(self):
        """Verify the configuration worked"""
        print(f"🧪 Verifying configuration...")
        
        # Wait a bit for device to apply settings
        time.sleep(5)
        
        try:
            from zk_biometric import ZKBiometricDevice
            
            device = ZKBiometricDevice(
                device_ip='*************',
                port=32150,
                timeout=10,
                device_id='181',
                use_cloud=False
            )
            
            if device.connect():
                print(f"✅ ZK protocol connection successful!")
                try:
                    users = device.get_users()
                    print(f"   📱 Found {len(users)} users")
                    device.disconnect()
                    return True
                except Exception as e:
                    print(f"   ⚠️ Connected but limited access: {e}")
                    device.disconnect()
                    return True
            else:
                print(f"❌ ZK protocol connection failed")
                return False
                
        except Exception as e:
            print(f"❌ Verification error: {e}")
            return False

def main():
    """Main auto-configuration function"""
    print("🤖 AUTOMATED ZK DEVICE CONFIGURATION")
    print("=" * 60)
    print("Device: *************")
    print("Target Settings:")
    print("   Device ID: 181")
    print("   Common Key: 1302")
    print("   Port: 32150")
    print("   TCP/IP: Enabled")
    print("=" * 60)
    
    config = AutoZKConfig()
    
    # Step 1: Login
    if not config.login():
        print(f"\n❌ Auto-configuration failed: Cannot login")
        return
    
    # Step 2: Configure communication
    if not config.configure_communication():
        print(f"\n❌ Auto-configuration failed: Cannot configure settings")
        return
    
    # Step 3: Restart device (optional)
    print(f"\n🔄 Restarting device to apply settings...")
    config.restart_device()
    
    # Wait for restart
    print(f"⏳ Waiting 30 seconds for device to restart...")
    time.sleep(30)
    
    # Step 4: Verify configuration
    if config.verify_configuration():
        print(f"\n🎉 SUCCESS! Device configured and working!")
        print(f"✅ Ethernet connection via ZK protocol is now active")
        print(f"✅ You can now use your ZK biometric applications")
    else:
        print(f"\n⚠️ Configuration applied but verification failed")
        print(f"Device may need manual restart or additional configuration")
    
    print(f"\n📋 Next Steps:")
    print(f"1. Test connection: python simple_test_181.py")
    print(f"2. Run your main application")
    print(f"3. If issues persist, try manual web interface configuration")

if __name__ == '__main__':
    main()
