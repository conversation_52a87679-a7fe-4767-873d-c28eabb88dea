#!/usr/bin/env python3
"""
Comprehensive Biometric Verification Test Suite

This script tests all aspects of the biometric verification functionality
in the VishnoRex attendance management system.
"""

import sqlite3
import os
import sys
import requests
import json
from datetime import datetime, timedelta


class BiometricVerificationTestSuite:
    """Test suite for biometric verification functionality"""
    
    def __init__(self):
        self.test_results = []
        self.base_url = "http://127.0.0.1:5000"
        self.session = requests.Session()
    
    def log_test(self, test_name, passed, message=""):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'name': test_name,
            'passed': passed,
            'message': message
        })
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
    
    def test_database_structure(self):
        """Test biometric_verifications table structure"""
        print("\n=== Testing Database Structure ===")
        
        try:
            conn = sqlite3.connect('vishnorex.db')
            cursor = conn.cursor()
            
            # Check if table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='biometric_verifications'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                self.log_test("Database - biometric_verifications table exists", True)
                
                # Check table structure
                cursor.execute('PRAGMA table_info(biometric_verifications)')
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]
                
                required_columns = [
                    'id', 'staff_id', 'school_id', 'verification_type', 
                    'verification_time', 'device_ip', 'biometric_method', 
                    'verification_status', 'notes'
                ]
                
                missing_columns = [col for col in required_columns if col not in column_names]
                
                if not missing_columns:
                    self.log_test("Database - All required columns present", True)
                else:
                    self.log_test("Database - All required columns present", False, f"Missing: {missing_columns}")
                
                # Check if there's data
                cursor.execute('SELECT COUNT(*) FROM biometric_verifications')
                count = cursor.fetchone()[0]
                
                if count > 0:
                    self.log_test("Database - Contains verification data", True, f"{count} records found")
                else:
                    self.log_test("Database - Contains verification data", False, "No records found")
                
            else:
                self.log_test("Database - biometric_verifications table exists", False)
            
            conn.close()
            
        except Exception as e:
            self.log_test("Database structure test", False, str(e))
    
    def test_api_endpoints(self):
        """Test biometric verification API endpoints"""
        print("\n=== Testing API Endpoints ===")
        
        try:
            # Test biometric connection endpoint
            response = self.session.post(f"{self.base_url}/test_biometric_connection", 
                                       data={'device_ip': '*************'})
            
            if response.status_code == 200:
                self.log_test("API - /test_biometric_connection endpoint accessible", True)
            else:
                self.log_test("API - /test_biometric_connection endpoint accessible", False, f"Status: {response.status_code}")
            
            # Test device verification endpoint (requires login)
            response = self.session.post(f"{self.base_url}/check_device_verification")
            
            # Should return 200 with error message (not logged in) or redirect
            if response.status_code in [200, 302]:
                self.log_test("API - /check_device_verification endpoint accessible", True)
            else:
                self.log_test("API - /check_device_verification endpoint accessible", False, f"Status: {response.status_code}")
            
        except Exception as e:
            self.log_test("API endpoints test", False, str(e))
    
    def test_verification_types(self):
        """Test different verification types are supported"""
        print("\n=== Testing Verification Types ===")
        
        try:
            conn = sqlite3.connect('vishnorex.db')
            cursor = conn.cursor()
            
            # Check for different verification types in the database
            cursor.execute('SELECT DISTINCT verification_type FROM biometric_verifications')
            verification_types = [row[0] for row in cursor.fetchall()]
            
            expected_types = ['check-in', 'check-out', 'overtime-in', 'overtime-out']
            
            for vtype in expected_types:
                if vtype in verification_types:
                    self.log_test(f"Verification Types - {vtype} supported", True)
                else:
                    self.log_test(f"Verification Types - {vtype} supported", False, "No records found")
            
            conn.close()
            
        except Exception as e:
            self.log_test("Verification types test", False, str(e))
    
    def test_recent_verifications_display(self):
        """Test recent verifications display functionality"""
        print("\n=== Testing Recent Verifications Display ===")
        
        try:
            conn = sqlite3.connect('vishnorex.db')
            cursor = conn.cursor()
            
            # Test query used in staff profile
            cursor.execute('''
                SELECT verification_type, verification_time, biometric_method, verification_status
                FROM biometric_verifications
                WHERE staff_id = ?
                ORDER BY verification_time DESC
                LIMIT 10
            ''', (20,))  # Using staff_id 20 from our database check
            
            results = cursor.fetchall()
            
            if results:
                self.log_test("Display - Recent verifications query works", True, f"Found {len(results)} records")
                
                # Check if results have all required fields
                first_result = results[0]
                if len(first_result) == 4:
                    self.log_test("Display - Query returns all required fields", True)
                else:
                    self.log_test("Display - Query returns all required fields", False, f"Expected 4 fields, got {len(first_result)}")
            else:
                self.log_test("Display - Recent verifications query works", False, "No results returned")
            
            conn.close()
            
        except Exception as e:
            self.log_test("Recent verifications display test", False, str(e))
    
    def test_verification_status_tracking(self):
        """Test verification status tracking"""
        print("\n=== Testing Verification Status Tracking ===")
        
        try:
            conn = sqlite3.connect('vishnorex.db')
            cursor = conn.cursor()
            
            # Check for different verification statuses
            cursor.execute('SELECT DISTINCT verification_status FROM biometric_verifications')
            statuses = [row[0] for row in cursor.fetchall()]
            
            if 'success' in statuses:
                self.log_test("Status Tracking - Success status recorded", True)
            else:
                self.log_test("Status Tracking - Success status recorded", False, "No success records found")
            
            # Check for biometric methods
            cursor.execute('SELECT DISTINCT biometric_method FROM biometric_verifications')
            methods = [row[0] for row in cursor.fetchall()]
            
            if 'fingerprint' in methods:
                self.log_test("Status Tracking - Fingerprint method recorded", True)
            else:
                self.log_test("Status Tracking - Fingerprint method recorded", False, "No fingerprint records found")
            
            conn.close()
            
        except Exception as e:
            self.log_test("Verification status tracking test", False, str(e))
    
    def test_time_based_queries(self):
        """Test time-based verification queries"""
        print("\n=== Testing Time-Based Queries ===")
        
        try:
            conn = sqlite3.connect('vishnorex.db')
            cursor = conn.cursor()
            
            # Test recent verifications (last 24 hours)
            yesterday = datetime.now() - timedelta(days=1)
            cursor.execute('''
                SELECT COUNT(*) FROM biometric_verifications
                WHERE verification_time >= ?
            ''', (yesterday,))
            
            recent_count = cursor.fetchone()[0]
            
            if recent_count >= 0:  # Should always be >= 0
                self.log_test("Time Queries - Recent verifications query works", True, f"Found {recent_count} recent records")
            else:
                self.log_test("Time Queries - Recent verifications query works", False, "Query failed")
            
            # Test today's verifications
            today = datetime.now().date()
            cursor.execute('''
                SELECT COUNT(*) FROM biometric_verifications
                WHERE DATE(verification_time) = ?
            ''', (today,))
            
            today_count = cursor.fetchone()[0]
            
            if today_count >= 0:
                self.log_test("Time Queries - Today's verifications query works", True, f"Found {today_count} today's records")
            else:
                self.log_test("Time Queries - Today's verifications query works", False, "Query failed")
            
            conn.close()
            
        except Exception as e:
            self.log_test("Time-based queries test", False, str(e))
    
    def test_frontend_components(self):
        """Test frontend component files exist"""
        print("\n=== Testing Frontend Components ===")
        
        # Check if staff dashboard template exists and contains biometric verification code
        if os.path.exists('templates/staff_my_profile.html'):
            try:
                with open('templates/staff_my_profile.html', 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'Recent Biometric Verifications' in content:
                    self.log_test("Frontend - Staff profile template has verification section", True)
                else:
                    self.log_test("Frontend - Staff profile template has verification section", False, "Section not found")
                
                if 'recent_verifications' in content:
                    self.log_test("Frontend - Template uses verification data", True)
                else:
                    self.log_test("Frontend - Template uses verification data", False, "Variable not found")
                
            except Exception as e:
                self.log_test("Frontend template check", False, str(e))
        else:
            self.log_test("Frontend - Staff profile template exists", False, "File not found")
        
        # Check JavaScript file
        if os.path.exists('static/js/staff_dashboard.js'):
            try:
                with open('static/js/staff_dashboard.js', 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'updateVerificationHistory' in content:
                    self.log_test("Frontend - JavaScript has verification functions", True)
                else:
                    self.log_test("Frontend - JavaScript has verification functions", False, "Function not found")
                
                if 'biometric' in content.lower():
                    self.log_test("Frontend - JavaScript handles biometric operations", True)
                else:
                    self.log_test("Frontend - JavaScript handles biometric operations", False, "No biometric code found")
                
            except Exception as e:
                self.log_test("Frontend JavaScript check", False, str(e))
        else:
            self.log_test("Frontend - Staff dashboard JavaScript exists", False, "File not found")
    
    def run_all_tests(self):
        """Run all biometric verification tests"""
        print("🔍 Starting Biometric Verification Test Suite")
        print("=" * 60)
        
        self.test_database_structure()
        self.test_api_endpoints()
        self.test_verification_types()
        self.test_recent_verifications_display()
        self.test_verification_status_tracking()
        self.test_time_based_queries()
        self.test_frontend_components()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 BIOMETRIC VERIFICATION TEST SUMMARY")
        print("=" * 60)
        
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        total_tests = len(self.test_results)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        # Show failed tests
        failed_tests = [result for result in self.test_results if not result['passed']]
        if failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in failed_tests:
                print(f"  - {test['name']}: {test['message']}")
        
        return passed_tests == total_tests


def main():
    """Main function"""
    print("🔍 VishnoRex Biometric Verification Test Suite")
    print("=" * 60)
    print("Testing all aspects of biometric verification functionality")
    print()
    
    test_suite = BiometricVerificationTestSuite()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n🎉 All biometric verification tests passed!")
        print("✅ The biometric verification system is working correctly")
        print("🚀 Recent Biometric Verifications functionality is fully operational")
        return True
    else:
        print("\n⚠️  Some biometric verification tests failed")
        print("📝 Please review the failed tests and address any issues")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
