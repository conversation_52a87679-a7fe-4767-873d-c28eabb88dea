import sqlite3

# Path to your SQLite database
DB_PATH = "vishnorex.db"

# Connect to the database
conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

# 1. Rename the old table
cursor.execute("ALTER TABLE staff RENAME TO staff_old;")

# 2. Create the new table without UNIQUE constraint
cursor.execute('''
CREATE TABLE staff (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    school_id INTEGER,
    staff_id TEXT,
    password_hash TEXT,
    full_name TEXT,
    email TEXT,
    phone TEXT,
    department TEXT,
    position TEXT,
    photo_url TEXT
);
''')

# 3. Copy data from old table
cursor.execute('''
INSERT INTO staff (id, school_id, staff_id, password_hash, full_name, email, phone, department, position, photo_url)
SELECT id, school_id, staff_id, password_hash, full_name, email, phone, department, position, photo_url FROM staff_old;
''')

# 4. Drop the old table
cursor.execute("DROP TABLE staff_old;")

conn.commit()
conn.close()

print("Migration complete. The UNIQUE constraint has been removed from staff table.")
