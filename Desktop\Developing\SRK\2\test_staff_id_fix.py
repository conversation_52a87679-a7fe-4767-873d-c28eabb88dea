#!/usr/bin/env python3
"""
Test Staff ID Fix
Test that the "Staff ID already exists" error is properly handled
"""

import sqlite3
import os

def test_staff_id_check():
    """Test the staff ID checking logic"""
    print("🧪 Testing Staff ID Check Logic")
    print("-" * 40)
    
    # Test database connection
    try:
        db_path = 'attendance.db'
        if not os.path.exists(db_path):
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        
        # Test query that checks for existing staff
        test_school_id = 4  # Assuming this is your school ID
        test_staff_id = "TEST123"
        
        # Check if staff exists
        existing_staff = conn.execute('''
            SELECT full_name FROM staff
            WHERE school_id = ? AND staff_id = ?
        ''', (test_school_id, test_staff_id)).fetchone()
        
        if existing_staff:
            print(f"✅ Found existing staff: {existing_staff['full_name']}")
            print("✅ Staff ID check logic should work correctly")
        else:
            print(f"✅ Staff ID {test_staff_id} is available")
            print("✅ New staff can be created with this ID")
        
        # Test with a known existing staff ID
        all_staff = conn.execute('''
            SELECT staff_id, full_name FROM staff
            WHERE school_id = ?
            LIMIT 5
        ''', (test_school_id,)).fetchall()
        
        print(f"\n📋 Sample existing staff IDs in school {test_school_id}:")
        for staff in all_staff:
            print(f"   - {staff['staff_id']}: {staff['full_name']}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error testing database: {e}")
        return False

def check_app_fix():
    """Check if the fix is properly implemented in app.py"""
    print("\n🔧 Checking App.py Fix Implementation")
    print("-" * 40)
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if the early staff ID check is present
        if 'existing_staff = db.execute(' in content and 'SELECT full_name FROM staff' in content:
            print("✅ Early staff ID check is implemented")
        else:
            print("❌ Early staff ID check not found")
            return False
        
        # Check if the error message is informative
        if 'Staff ID' in content and 'already exists in this school' in content:
            print("✅ Informative error message is present")
        else:
            print("❌ Informative error message not found")
            return False
        
        # Check if suggestion is provided
        if 'Please use a different Staff ID' in content:
            print("✅ Helpful suggestion is provided")
        else:
            print("❌ Helpful suggestion not found")
            return False
        
        print("✅ All fix components are properly implemented")
        return True
        
    except Exception as e:
        print(f"❌ Error checking app.py: {e}")
        return False

def provide_testing_steps():
    """Provide steps to test the fix"""
    print("\n📋 TESTING STEPS FOR THE FIX")
    print("=" * 50)
    
    print("1. 🔄 Restart your Flask application")
    print("   - Stop current app (Ctrl+C)")
    print("   - Run: python app.py")
    print()
    
    print("2. 🧪 Test with existing staff ID:")
    print("   - Go to Admin Dashboard")
    print("   - Try to add new staff with existing Staff ID")
    print("   - Should get clear error message immediately")
    print("   - Should NOT go through biometric verification first")
    print()
    
    print("3. ✅ Test with new staff ID:")
    print("   - Try to add staff with unique Staff ID")
    print("   - Should proceed to biometric verification")
    print("   - Should work normally")
    print()
    
    print("4. 🎯 Expected behavior:")
    print("   - ✅ Early detection of duplicate Staff IDs")
    print("   - ✅ Clear error message with existing staff name")
    print("   - ✅ Helpful suggestion to use different ID")
    print("   - ✅ No unnecessary biometric verification for duplicates")

def main():
    """Main test function"""
    print("🔧 STAFF ID FIX VERIFICATION")
    print("=" * 60)
    print("Testing the fix for 'Staff ID already exists' error")
    print("=" * 60)
    
    # Test database logic
    db_test_passed = test_staff_id_check()
    
    # Check app implementation
    app_fix_passed = check_app_fix()
    
    print(f"\n📊 TEST RESULTS:")
    print(f"   Database Logic: {'✅ PASSED' if db_test_passed else '❌ FAILED'}")
    print(f"   App Implementation: {'✅ PASSED' if app_fix_passed else '❌ FAILED'}")
    
    if db_test_passed and app_fix_passed:
        print(f"\n🎉 SUCCESS! Fix is properly implemented")
        print("✅ Staff ID duplicates will be caught early")
        print("✅ Users will get clear error messages")
        print("✅ No more unnecessary verification steps")
    else:
        print(f"\n❌ Issues detected - fix may not work properly")
    
    # Provide testing steps
    provide_testing_steps()

if __name__ == '__main__':
    main()
