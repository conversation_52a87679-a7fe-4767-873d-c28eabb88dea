#!/usr/bin/env python3
"""
Check Device 181 Access Methods
Find out why web interface isn't working and what alternatives we have
"""

import subprocess
import socket
import requests
import platform
from urllib3.exceptions import InsecureRequestWarning
import urllib3

urllib3.disable_warnings(InsecureRequestWarning)

def test_ping():
    """Test if device responds to ping"""
    print("🌐 Testing Network Connectivity")
    print("-" * 40)
    
    try:
        if platform.system().lower() == "windows":
            result = subprocess.run(['ping', '-n', '4', '*************'], 
                                  capture_output=True, text=True, timeout=15)
        else:
            result = subprocess.run(['ping', '-c', '4', '*************'], 
                                  capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✅ Device responds to ping")
            # Extract timing info
            lines = result.stdout.split('\n')
            for line in lines:
                if 'time=' in line.lower() or 'average' in line.lower():
                    print(f"   {line.strip()}")
            return True
        else:
            print("❌ Device does not respond to ping")
            print("   Device may be offline or unreachable")
            return False
            
    except Exception as e:
        print(f"❌ Ping test failed: {e}")
        return False

def test_ports():
    """Test which ports are accessible"""
    print("\n🔌 Testing Port Accessibility")
    print("-" * 40)
    
    ports_to_test = [80, 443, 8080, 32150, 4370, 8000, 9000]
    accessible_ports = []
    
    for port in ports_to_test:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('*************', port))
            sock.close()
            
            if result == 0:
                print(f"✅ Port {port}: Accessible")
                accessible_ports.append(port)
            else:
                print(f"❌ Port {port}: Not accessible")
                
        except Exception as e:
            print(f"❌ Port {port}: Error - {e}")
    
    return accessible_ports

def test_web_interfaces():
    """Test different web interface URLs"""
    print("\n🌐 Testing Web Interface URLs")
    print("-" * 40)
    
    urls_to_test = [
        "http://*************",
        "http://*************:80",
        "http://*************:8080",
        "http://*************:32150",
        "https://*************",
        "https://*************:443",
        "https://*************:32150"
    ]
    
    working_urls = []
    
    for url in urls_to_test:
        try:
            print(f"Testing: {url}")
            response = requests.get(url, timeout=10, verify=False)
            
            if response.status_code == 200:
                print(f"✅ {url} - Working (Status: {response.status_code})")
                content_type = response.headers.get('content-type', 'unknown')
                print(f"   Content-Type: {content_type}")
                
                # Check if it looks like a device interface
                content = response.text.lower()
                if any(keyword in content for keyword in ['zkteco', 'biometric', 'attendance', 'device', 'login']):
                    print(f"   🎯 Likely device interface!")
                    working_urls.append(url)
                else:
                    print(f"   ℹ️ Web service (may not be device)")
                    
            elif response.status_code == 401:
                print(f"🔐 {url} - Requires authentication")
                working_urls.append(url)
            elif response.status_code == 403:
                print(f"🚫 {url} - Access forbidden")
            else:
                print(f"⚠️ {url} - Status: {response.status_code}")
                
        except requests.exceptions.ConnectTimeout:
            print(f"⏱️ {url} - Connection timeout")
        except requests.exceptions.ConnectionError:
            print(f"❌ {url} - Connection refused")
        except Exception as e:
            print(f"❌ {url} - Error: {str(e)[:50]}...")
    
    return working_urls

def test_zk_protocol():
    """Test ZK protocol connection"""
    print("\n🔗 Testing ZK Protocol Connection")
    print("-" * 40)
    
    try:
        from zk_biometric import ZKBiometricDevice
        
        print("Testing ZK protocol on port 32150...")
        device = ZKBiometricDevice(
            device_ip='*************',
            port=32150,
            timeout=15,
            device_id='181',
            use_cloud=False
        )
        
        if device.connect():
            print("✅ ZK protocol connection: SUCCESS")
            try:
                users = device.get_users()
                print(f"   Users found: {len(users)}")
                device.disconnect()
                return True
            except Exception as e:
                print(f"   ⚠️ Connected but limited access: {e}")
                device.disconnect()
                return True
        else:
            print("❌ ZK protocol connection: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ ZK protocol error: {e}")
        return False

def provide_solutions(ping_ok, accessible_ports, working_urls, zk_works):
    """Provide solutions based on test results"""
    print("\n" + "=" * 50)
    print("💡 DIAGNOSIS AND SOLUTIONS")
    print("=" * 50)
    
    if not ping_ok:
        print("🚨 CRITICAL: Device not responding to ping")
        print("Solutions:")
        print("1. Check if device is powered on")
        print("2. Verify network cable connection")
        print("3. Check if IP ************* is correct")
        print("4. Verify device is on same network")
        return
    
    if not accessible_ports:
        print("🚨 CRITICAL: No ports accessible")
        print("Solutions:")
        print("1. Device may be behind firewall")
        print("2. Check device network configuration")
        print("3. Verify device services are running")
        return
    
    if not working_urls:
        print("⚠️ Web interface not accessible")
        print("Solutions:")
        print("1. Try alternative configuration methods:")
        print("   - Physical device menu (if available)")
        print("   - ZKTeco official software")
        print("   - Different web browser")
        print("2. Check if device requires specific authentication")
        
        if 80 in accessible_ports or 443 in accessible_ports:
            print("3. Try these URLs manually:")
            if 80 in accessible_ports:
                print("   http://*************:80")
            if 443 in accessible_ports:
                print("   https://*************:443")
    else:
        print("✅ Web interface found!")
        print("Working URLs:")
        for url in working_urls:
            print(f"   🔗 {url}")
    
    if zk_works:
        print("\n🎉 GOOD NEWS: ZK protocol is working!")
        print("✅ Your device can be used even without web interface")
        print("✅ Your app.py will work with this device")
    else:
        print("\n⚠️ ZK protocol needs configuration")
        print("Device needs to be configured for PC connection")

def main():
    """Main diagnostic function"""
    print("🔧 DEVICE 181 - ACCESS DIAGNOSTIC")
    print("=" * 50)
    print("Device: *************")
    print("Checking all access methods...")
    print("=" * 50)
    
    # Run all tests
    ping_ok = test_ping()
    accessible_ports = test_ports()
    working_urls = test_web_interfaces()
    zk_works = test_zk_protocol()
    
    # Provide solutions
    provide_solutions(ping_ok, accessible_ports, working_urls, zk_works)
    
    print("\n📋 SUMMARY:")
    print(f"Network: {'✅' if ping_ok else '❌'}")
    print(f"Ports: {len(accessible_ports)} accessible")
    print(f"Web Interface: {'✅' if working_urls else '❌'}")
    print(f"ZK Protocol: {'✅' if zk_works else '❌'}")

if __name__ == '__main__':
    main()
