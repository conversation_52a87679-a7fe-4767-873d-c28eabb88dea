#!/usr/bin/env python3
"""
Complete Dual Connectivity Test for Device 181
Tests both Ethernet and Cloud connections
"""

from zk_biometric import ZKBiometricDevice
import time

def test_ethernet_connection():
    """Test direct Ethernet connection"""
    print("🔌 TESTING ETHERNET CONNECTION")
    print("-" * 40)
    
    try:
        device = ZKBiometricDevice(
            device_ip='*************',
            port=32150,
            timeout=15,
            device_id='181',
            use_cloud=False  # Force Ethernet
        )
        
        print("Connecting via Ethernet...")
        if device.connect():
            print("✅ Ethernet connection: SUCCESS")
            try:
                users = device.get_users()
                print(f"   Users: {len(users)}")
                device.disconnect()
                return True
            except Exception as e:
                print(f"   ⚠️ Connected but limited access: {e}")
                device.disconnect()
                return True
        else:
            print("❌ Ethernet connection: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Ethernet error: {e}")
        return False

def test_cloud_connection():
    """Test cloud connection"""
    print("\n☁️ TESTING CLOUD CONNECTION")
    print("-" * 40)
    
    try:
        device = ZKBiometricDevice(
            device_id='181',
            use_cloud=True  # Force Cloud
        )
        
        print("Connecting via Cloud...")
        if device.connect():
            print("✅ Cloud connection: SUCCESS")
            try:
                users = device.get_users()
                print(f"   Users: {len(users)}")
                device.disconnect()
                return True
            except Exception as e:
                print(f"   ⚠️ Connected but limited access: {e}")
                device.disconnect()
                return True
        else:
            print("❌ Cloud connection: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Cloud error: {e}")
        return False

def test_auto_detection():
    """Test auto-detection mode"""
    print("\n🔄 TESTING AUTO-DETECTION MODE")
    print("-" * 40)
    
    try:
        device = ZKBiometricDevice(
            device_ip='*************',  # Ethernet fallback
            device_id='181',            # Cloud device ID
            port=32150,
            timeout=15
            # use_cloud=None (default) - auto-detect
        )
        
        print("Connecting with auto-detection...")
        if device.connect():
            print("✅ Auto-detection: SUCCESS")
            print(f"   Connection mode: {getattr(device, 'connection_mode', 'Unknown')}")
            try:
                users = device.get_users()
                print(f"   Users: {len(users)}")
                device.disconnect()
                return True
            except Exception as e:
                print(f"   ⚠️ Connected but limited access: {e}")
                device.disconnect()
                return True
        else:
            print("❌ Auto-detection: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Auto-detection error: {e}")
        return False

def main():
    """Main test function"""
    print("🎯 DEVICE 181 - DUAL CONNECTIVITY TEST")
    print("=" * 50)
    print("Device IP: *************:32150")
    print("Device ID: 181")
    print("Common Key: 1302")
    print("=" * 50)
    
    # Test all connection methods
    ethernet_works = test_ethernet_connection()
    cloud_works = test_cloud_connection()
    auto_works = test_auto_detection()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 CONNECTIVITY TEST RESULTS")
    print("=" * 50)
    print(f"Ethernet Connection: {'✅ WORKING' if ethernet_works else '❌ FAILED'}")
    print(f"Cloud Connection:    {'✅ WORKING' if cloud_works else '❌ FAILED'}")
    print(f"Auto-Detection:      {'✅ WORKING' if auto_works else '❌ FAILED'}")
    
    # Recommendations
    print("\n🎯 RECOMMENDATIONS:")
    if ethernet_works and cloud_works:
        print("🎉 PERFECT! Both connections working!")
        print("✅ Your device supports full dual connectivity")
        print("✅ Use auto-detection mode for best results")
    elif ethernet_works:
        print("✅ Ethernet working, Cloud needs configuration")
        print("💡 Configure cloud authentication in device")
    elif cloud_works:
        print("✅ Cloud working, Ethernet needs configuration")
        print("💡 Check device TCP/IP settings")
    else:
        print("⚠️ Both connections need configuration")
        print("💡 Check device web interface settings")
    
    print("\n📋 NEXT STEPS:")
    if not ethernet_works:
        print("1. Configure device via web interface: http://*************")
        print("2. Set Device ID = 181, Common Key = 1302")
        print("3. Enable TCP/IP communication")
    
    if not cloud_works:
        print("4. Configure cloud authentication")
        print("5. Enable cloud mode in device settings")
    
    if ethernet_works or cloud_works:
        print("6. Your app.py will now work with Device 181!")
        print("7. Start your Flask app: python app.py")

if __name__ == '__main__':
    main()
