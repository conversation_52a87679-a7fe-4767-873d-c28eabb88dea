#!/usr/bin/env python3
"""
Database Schema Checker

Check for regularization and notification related tables and columns
"""

import sqlite3

def check_database_schema():
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()

    print("🔍 Checking Database Schema for Regularization and Notification Components")
    print("=" * 70)

    # Check for regularization-related tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%regularization%'")
    reg_tables = cursor.fetchall()
    print("\n📋 Regularization tables found:")
    if reg_tables:
        for table in reg_tables:
            print(f"  - {table[0]}")
    else:
        print("  - None found")

    # Check attendance table columns for regularization
    cursor.execute('PRAGMA table_info(attendance)')
    attendance_columns = cursor.fetchall()
    print("\n📊 Attendance table regularization columns:")
    reg_columns = [col for col in attendance_columns if 'regularization' in col[1]]
    if reg_columns:
        for col in reg_columns:
            print(f"  - {col[1]} ({col[2]})")
    else:
        print("  - None found")

    # Check for notification tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%notification%'")
    notif_tables = cursor.fetchall()
    print("\n🔔 Notification tables found:")
    if notif_tables:
        for table in notif_tables:
            print(f"  - {table[0]}")
    else:
        print("  - None found")

    # Show all tables for reference
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    all_tables = cursor.fetchall()
    print(f"\n📚 All tables in database ({len(all_tables)} total):")
    for table in all_tables:
        print(f"  - {table[0]}")

    conn.close()

if __name__ == "__main__":
    check_database_schema()
