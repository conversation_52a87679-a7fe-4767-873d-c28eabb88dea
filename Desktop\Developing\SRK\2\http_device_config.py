#!/usr/bin/env python3
"""
HTTP-based ZK Device Configuration
Since the device responds to HTTP, try to configure it via HTTP API
"""

import requests
import json
import base64
import time
from urllib.parse import urljoin
import urllib3

# Disable SSL warnings for self-signed certificates
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class HTTPZKConfig:
    """HTTP-based ZK device configuration"""
    
    def __init__(self, ip="*************", port=None):
        self.ip = ip
        self.base_urls = []
        
        # Try different ports and protocols
        if port:
            self.base_urls = [f"http://{ip}:{port}", f"https://{ip}:{port}"]
        else:
            self.base_urls = [
                f"http://{ip}",
                f"https://{ip}",
                f"http://{ip}:32150",
                f"https://{ip}:32150",
                f"http://{ip}:80",
                f"https://{ip}:443",
                f"http://{ip}:8080"
            ]
        
        self.session = requests.Session()
        self.session.verify = False  # Ignore SSL certificate issues
        self.working_url = None
        
    def find_working_url(self):
        """Find a working HTTP URL"""
        print("🔍 Finding working HTTP endpoint...")
        
        for url in self.base_urls:
            try:
                print(f"Testing {url}...", end=" ")
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    print("✅ Working!")
                    self.working_url = url
                    
                    # Check if it looks like a ZK device
                    content = response.text.lower()
                    if any(keyword in content for keyword in ['zkteco', 'attendance', 'biometric', 'device', 'login']):
                        print(f"   🎯 Appears to be ZK device interface")
                        return url
                    else:
                        print(f"   ⚠️ HTTP working but may not be ZK device")
                        
                else:
                    print(f"❌ HTTP {response.status_code}")
                    
            except requests.exceptions.SSLError:
                print("⚠️ SSL issue (device accessible)")
                self.working_url = url
                return url
            except requests.exceptions.ConnectTimeout:
                print("❌ Timeout")
            except requests.exceptions.ConnectionError:
                print("❌ Connection refused")
            except Exception as e:
                print(f"❌ Error: {e}")
        
        return None
    
    def try_common_login_paths(self):
        """Try common login paths"""
        if not self.working_url:
            return None
            
        print(f"\n🔐 Testing common login paths...")
        
        login_paths = [
            "/",
            "/login",
            "/admin",
            "/index.html",
            "/login.html",
            "/admin.html",
            "/cgi-bin/login",
            "/web/login",
            "/system/login"
        ]
        
        for path in login_paths:
            try:
                url = urljoin(self.working_url, path)
                print(f"Testing {path}...", end=" ")
                
                response = self.session.get(url, timeout=5)
                
                if response.status_code == 200:
                    content = response.text.lower()
                    
                    if any(keyword in content for keyword in ['login', 'password', 'username', 'admin']):
                        print("✅ Login page found!")
                        return url
                    else:
                        print("⚠️ Page found but no login form")
                else:
                    print(f"❌ {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
        
        return None
    
    def try_default_credentials(self, login_url):
        """Try common default credentials"""
        if not login_url:
            return False
            
        print(f"\n🔑 Trying default credentials...")
        
        # Common ZK device credentials
        credentials = [
            {"username": "admin", "password": "123456"},
            {"username": "admin", "password": "admin"},
            {"username": "admin", "password": ""},
            {"username": "administrator", "password": "123456"},
            {"username": "root", "password": "123456"},
            {"username": "admin", "password": "password"},
        ]
        
        for cred in credentials:
            print(f"Trying {cred['username']}/{cred['password'] or '(empty)'}...", end=" ")
            
            try:
                # Try different login methods
                login_data = {
                    "username": cred["username"],
                    "password": cred["password"],
                    "user": cred["username"],
                    "pass": cred["password"],
                    "login": "Login",
                    "submit": "Login"
                }
                
                response = self.session.post(login_url, data=login_data, timeout=10)
                
                if response.status_code == 200:
                    # Check if login was successful
                    content = response.text.lower()
                    
                    if any(success in content for success in ['dashboard', 'main', 'system', 'device', 'logout']):
                        print("✅ SUCCESS!")
                        return True
                    elif any(fail in content for fail in ['invalid', 'error', 'failed', 'incorrect']):
                        print("❌ Invalid credentials")
                    else:
                        print("⚠️ Unknown response")
                else:
                    print(f"❌ HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
        
        return False
    
    def explore_device_api(self):
        """Explore device API endpoints"""
        if not self.working_url:
            return
            
        print(f"\n🔍 Exploring device API endpoints...")
        
        api_paths = [
            "/api",
            "/api/device",
            "/api/system",
            "/api/config",
            "/cgi-bin/api",
            "/device/api",
            "/system/api",
            "/rest/api",
            "/v1/api"
        ]
        
        for path in api_paths:
            try:
                url = urljoin(self.working_url, path)
                print(f"Testing {path}...", end=" ")
                
                response = self.session.get(url, timeout=5)
                
                if response.status_code == 200:
                    print("✅ API endpoint found!")
                    
                    # Try to parse JSON response
                    try:
                        data = response.json()
                        print(f"   JSON response: {str(data)[:100]}...")
                    except:
                        print(f"   Text response: {response.text[:100]}...")
                        
                elif response.status_code == 401:
                    print("🔐 Authentication required")
                elif response.status_code == 403:
                    print("🚫 Access forbidden")
                else:
                    print(f"❌ {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error: {e}")

def try_zk_software_protocol():
    """Try to communicate using ZK software protocol over HTTP"""
    print(f"\n🔧 Testing ZK Software Protocol over HTTP...")
    print("-" * 50)
    
    try:
        # Try to send ZK-style commands via HTTP
        config = HTTPZKConfig()
        working_url = config.find_working_url()
        
        if working_url:
            # Try common ZK HTTP API endpoints
            zk_endpoints = [
                "/zkapi/device/info",
                "/api/device/status",
                "/cgi-bin/device",
                "/device/status",
                "/system/info"
            ]
            
            for endpoint in zk_endpoints:
                try:
                    url = urljoin(working_url, endpoint)
                    response = config.session.get(url, timeout=5)
                    
                    if response.status_code == 200:
                        print(f"✅ {endpoint}: Working")
                        print(f"   Response: {response.text[:200]}...")
                        return True
                        
                except Exception as e:
                    continue
        
        return False
        
    except Exception as e:
        print(f"❌ ZK protocol test failed: {e}")
        return False

def provide_manual_configuration_guide():
    """Provide manual configuration guide"""
    print(f"\n📋 MANUAL CONFIGURATION GUIDE")
    print("=" * 50)
    
    print("Since automated configuration is challenging, here's what to do:")
    print()
    
    print("🌐 Web Interface Access:")
    print("   1. Open browser and go to: http://*************")
    print("   2. Try these login credentials:")
    print("      - admin / 123456")
    print("      - admin / admin")
    print("      - administrator / 123456")
    print()
    
    print("🔧 Configuration Steps:")
    print("   1. Look for 'System' or 'Communication' menu")
    print("   2. Find 'TCP/IP' or 'Network' settings")
    print("   3. Enable TCP/IP communication")
    print("   4. Set Device ID to: 181")
    print("   5. Set Common Key to: 1302")
    print("   6. Set Port to: 32150")
    print("   7. Save settings and restart device")
    print()
    
    print("🔄 Alternative Reset Methods:")
    print("   1. Physical reset button (hold 10+ seconds while powering on)")
    print("   2. Menu reset (if device has display/keypad)")
    print("   3. Factory reset via device menu")
    print()
    
    print("📞 If All Else Fails:")
    print("   1. Check device manual for specific reset procedure")
    print("   2. Contact device manufacturer support")
    print("   3. Use manufacturer's configuration software")

def main():
    """Main HTTP configuration function"""
    print("🌐 HTTP-BASED ZK DEVICE CONFIGURATION")
    print("=" * 60)
    print("Device: *************")
    print("Testing HTTP-based configuration methods")
    print("=" * 60)
    
    # Initialize HTTP config
    config = HTTPZKConfig()
    
    # Find working URL
    working_url = config.find_working_url()
    
    if working_url:
        print(f"\n✅ Working URL found: {working_url}")
        
        # Try to find login page
        login_url = config.try_common_login_paths()
        
        if login_url:
            # Try default credentials
            login_success = config.try_default_credentials(login_url)
            
            if login_success:
                print(f"\n🎉 Successfully logged into device!")
                print(f"You can now configure the device via web interface")
            else:
                print(f"\n⚠️ Could not login with default credentials")
        
        # Explore API endpoints
        config.explore_device_api()
        
    else:
        print(f"\n❌ No working HTTP endpoint found")
    
    # Try ZK software protocol
    zk_protocol_success = try_zk_software_protocol()
    
    # Provide manual guide
    provide_manual_configuration_guide()
    
    print(f"\n📋 SUMMARY:")
    print(f"   HTTP Access: {'Available' if working_url else 'Not found'}")
    print(f"   ZK Protocol: {'Working' if zk_protocol_success else 'Needs configuration'}")
    print(f"   Next Step: Manual web interface configuration")

if __name__ == '__main__':
    main()
