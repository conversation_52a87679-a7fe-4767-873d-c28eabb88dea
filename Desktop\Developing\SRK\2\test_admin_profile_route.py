#!/usr/bin/env python3
"""
Test the admin staff profile route specifically
"""

import sqlite3
import datetime
import json

def test_comprehensive_staff_profile():
    """Test the get_comprehensive_staff_profile route logic"""
    print("=== Testing get_comprehensive_staff_profile Route ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get a staff member to test with
        cursor.execute('SELECT id, staff_id, full_name FROM staff LIMIT 1')
        staff_record = cursor.fetchone()
        
        if not staff_record:
            print("❌ No staff records found")
            return False
            
        staff_id = staff_record['id']
        print(f"Testing with staff ID: {staff_id} ({staff_record['full_name']})")
        
        # Simulate the route logic
        # Get staff information
        staff = cursor.execute('''
            SELECT s.*, sc.name as school_name
            FROM staff s
            LEFT JOIN schools sc ON s.school_id = sc.id
            WHERE s.id = ?
        ''', (staff_id,)).fetchone()
        
        if not staff:
            print("❌ Staff not found")
            return False
            
        print(f"✅ Staff found: {staff['full_name']}")
        
        # Get attendance records (last 30 days)
        thirty_days_ago = (datetime.datetime.now() - datetime.timedelta(days=30)).date()
        attendance = cursor.execute('''
            SELECT date, time_in, time_out, overtime_in, overtime_out, status
            FROM attendance
            WHERE staff_id = ? AND date >= ?
            ORDER BY date DESC
        ''', (staff_id, thirty_days_ago)).fetchall()
        
        print(f"✅ Attendance records (last 30 days): {len(attendance)}")
        
        # Get biometric verifications (last 30 days)
        verifications = cursor.execute('''
            SELECT verification_type, verification_time, verification_status, device_ip
            FROM biometric_verifications
            WHERE staff_id = ? AND DATE(verification_time) >= ?
            ORDER BY verification_time DESC
            LIMIT 50
        ''', (staff_id, thirty_days_ago)).fetchall()
        
        print(f"✅ Biometric verifications (last 30 days): {len(verifications)}")
        
        # Show some verification samples
        if verifications:
            print("Sample verifications:")
            for i, v in enumerate(verifications[:5]):
                print(f"  {i+1}. {v['verification_time']}: {v['verification_type']} - {v['verification_status']} ({v['device_ip']})")
        else:
            print("❌ No verifications found!")
            
        # Get leave applications
        leaves = cursor.execute('''
            SELECT leave_type, start_date, end_date, reason, status, applied_at
            FROM leave_applications
            WHERE staff_id = ?
            ORDER BY applied_at DESC
            LIMIT 20
        ''', (staff_id,)).fetchall()
        
        print(f"✅ Leave applications: {len(leaves)}")
        
        # Calculate attendance statistics
        total_days = len(attendance)
        present_days = len([a for a in attendance if a['status'] in ['present', 'late']])
        absent_days = len([a for a in attendance if a['status'] == 'absent'])
        late_days = len([a for a in attendance if a['status'] == 'late'])
        
        attendance_stats = {
            'total_days': total_days,
            'present_days': present_days,
            'absent_days': absent_days,
            'late_days': late_days,
            'attendance_percentage': round((present_days / total_days * 100) if total_days > 0 else 0, 1)
        }
        
        print(f"✅ Attendance stats: {attendance_stats}")
        
        # Simulate the response
        response_data = {
            'success': True,
            'staff': dict(staff),
            'attendance': [dict(a) for a in attendance],
            'verifications': [dict(v) for v in verifications],
            'leaves': [dict(l) for l in leaves],
            'attendance_stats': attendance_stats
        }
        
        print(f"\n✅ Response would contain:")
        print(f"  - Staff info: ✅")
        print(f"  - Attendance records: {len(response_data['attendance'])}")
        print(f"  - Verifications: {len(response_data['verifications'])}")
        print(f"  - Leaves: {len(response_data['leaves'])}")
        print(f"  - Stats: {response_data['attendance_stats']}")
        
        # Test with different staff members
        print(f"\n=== Testing All Staff Members ===")
        cursor.execute('SELECT id, staff_id, full_name FROM staff')
        all_staff = cursor.fetchall()
        
        for staff_member in all_staff:
            staff_id = staff_member['id']
            
            # Count verifications for this staff member
            cursor.execute('''
                SELECT COUNT(*) as count
                FROM biometric_verifications
                WHERE staff_id = ? AND DATE(verification_time) >= ?
            ''', (staff_id, thirty_days_ago))
            
            verification_count = cursor.fetchone()['count']
            
            print(f"  - {staff_member['staff_id']} ({staff_member['full_name']}): {verification_count} verifications")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing route: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("🔍 Testing Admin Staff Profile Route")
    print("=" * 50)
    
    success = test_comprehensive_staff_profile()
    
    if success:
        print("\n✅ Admin staff profile route test completed")
    else:
        print("\n❌ Admin staff profile route test failed")
