<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Department Shift Management - VishnoRex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">VishnoRex - Department Shift Management</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-label="Toggle navigation" title="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin_dashboard') }}">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('staff_management') }}">Staff Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="#">Department Shifts</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ session.full_name }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- CSRF Token for AJAX requests -->
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Department Shift Assignments</h5>
                        <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addMappingModal">
                            <i class="bi bi-plus-circle"></i> Add Department Mapping
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>Department Shift Management:</strong> Configure default shift types for each department. 
                            When creating new staff members, their shift type will be automatically assigned based on their department.
                        </div>

                        <!-- Current Mappings Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="mappingsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Department</th>
                                        <th>Default Shift Type</th>
                                        <th>Created</th>
                                        <th>Last Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for mapping in mappings %}
                                    <tr data-department="{{ mapping.department }}">
                                        <td><strong>{{ mapping.department }}</strong></td>
                                        <td>
                                            <span class="badge bg-info">{{ mapping.default_shift_type.title() }}</span>
                                        </td>
                                        <td>{{ mapping.created_at[:10] if mapping.created_at else 'N/A' }}</td>
                                        <td>{{ mapping.updated_at[:10] if mapping.updated_at else 'N/A' }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary edit-mapping-btn" 
                                                        data-department="{{ mapping.department }}"
                                                        data-shift-type="{{ mapping.default_shift_type }}"
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#editMappingModal">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger delete-mapping-btn" 
                                                        data-department="{{ mapping.department }}">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        {% if not mappings %}
                        <div class="text-center py-4">
                            <i class="bi bi-building display-1 text-muted"></i>
                            <h4 class="text-muted">No Department Mappings Found</h4>
                            <p class="text-muted">Click "Add Department Mapping" to configure shift assignments for your departments.</p>
                        </div>
                        {% endif %}

                        <!-- Bulk Actions -->
                        {% if departments %}
                        <div class="mt-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Bulk Actions</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <button class="btn btn-warning btn-sm" id="bulkUpdateBtn">
                                                <i class="bi bi-arrow-repeat"></i> Update Existing Staff Shifts
                                            </button>
                                            <small class="form-text text-muted">Apply department shift rules to existing staff members</small>
                                        </div>
                                        <div class="col-md-6">
                                            <button class="btn btn-info btn-sm" id="previewChangesBtn">
                                                <i class="bi bi-eye"></i> Preview Changes
                                            </button>
                                            <small class="form-text text-muted">See which staff will be affected</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Mapping Modal -->
    <div class="modal fade" id="addMappingModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">Add Department Shift Mapping</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="addMappingForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="addDepartment" class="form-label">Department *</label>
                            <select class="form-select" id="addDepartment" name="department" required>
                                <option value="">Select Department</option>
                                {% for dept in departments %}
                                <option value="{{ dept.department }}">{{ dept.department }}</option>
                                {% endfor %}
                                <option value="custom">+ Add Custom Department</option>
                            </select>
                        </div>
                        <div class="mb-3" id="customDepartmentDiv" style="display: none;">
                            <label for="customDepartment" class="form-label">Custom Department Name *</label>
                            <input type="text" class="form-control" id="customDepartment" name="custom_department">
                        </div>
                        <div class="mb-3">
                            <label for="addShiftType" class="form-label">Default Shift Type *</label>
                            <select class="form-select" id="addShiftType" name="shift_type" required>
                                <option value="">Select Shift Type</option>
                                <option value="general">General</option>
                                <option value="morning">Morning</option>
                                <option value="evening">Evening</option>
                                <option value="night">Night</option>
                            </select>
                        </div>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>Note:</strong> This will set the default shift type for new staff members in this department. 
                            Existing staff shifts will not be changed automatically.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Add Mapping</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Mapping Modal -->
    <div class="modal fade" id="editMappingModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">Edit Department Shift Mapping</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editMappingForm">
                    <input type="hidden" id="editOriginalDepartment" name="original_department">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="editDepartment" class="form-label">Department</label>
                            <input type="text" class="form-control" id="editDepartment" name="department" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="editShiftType" class="form-label">Default Shift Type *</label>
                            <select class="form-select" id="editShiftType" name="shift_type" required>
                                <option value="general">General</option>
                                <option value="morning">Morning</option>
                                <option value="evening">Evening</option>
                                <option value="night">Night</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Mapping</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/department_shifts.js') }}"></script>
</body>
</html>
