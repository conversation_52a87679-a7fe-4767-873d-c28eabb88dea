#!/usr/bin/env python3
"""
Test script to verify staff creation functionality and database schema
"""

import sqlite3
import sys
from werkzeug.security import generate_password_hash

def check_database_schema():
    """Check the current database schema for staff table"""
    try:
        conn = sqlite3.connect('vishnorex.db')
        cursor = conn.cursor()
        
        # Get table info
        cursor.execute("PRAGMA table_info(staff)")
        columns = cursor.fetchall()
        
        print("Current staff table schema:")
        print("Column ID | Name | Type | NotNull | Default | PrimaryKey")
        print("-" * 60)
        
        required_columns = []
        for col in columns:
            col_id, name, col_type, not_null, default, pk = col
            print(f"{col_id:9} | {name:15} | {col_type:10} | {not_null:7} | {str(default):10} | {pk}")
            if not_null:
                required_columns.append(name)
        
        print(f"\nRequired (NOT NULL) columns: {required_columns}")
        
        conn.close()
        return required_columns
        
    except Exception as e:
        print(f"Error checking database schema: {e}")
        return []

def test_staff_insertion():
    """Test staff insertion with proper values for all required fields"""
    try:
        conn = sqlite3.connect('vishnorex.db')
        cursor = conn.cursor()
        
        # Test data
        test_data = {
            'school_id': 1,
            'staff_id': 'TEST001',
            'password': generate_password_hash('testpassword123'),
            'password_hash': generate_password_hash('testpassword123'),
            'full_name': 'Test User',
            'first_name': 'Test',
            'last_name': 'User',
            'email': '<EMAIL>',
            'phone': '1234567890',
            'department': 'Testing',
            'position': 'Test Staff',
            'shift_type': 'general'
        }
        
        # Check if test staff already exists
        cursor.execute("SELECT id FROM staff WHERE staff_id = ?", (test_data['staff_id'],))
        existing = cursor.fetchone()
        
        if existing:
            print(f"Test staff with ID {test_data['staff_id']} already exists. Deleting...")
            cursor.execute("DELETE FROM staff WHERE staff_id = ?", (test_data['staff_id'],))
            conn.commit()
        
        # Get current columns
        cursor.execute("PRAGMA table_info(staff)")
        columns_info = cursor.fetchall()
        available_columns = [col[1] for col in columns_info]
        
        # Build insert query with only available columns
        insert_columns = []
        insert_values = []
        
        for key, value in test_data.items():
            if key in available_columns:
                insert_columns.append(key)
                insert_values.append(value)
        
        placeholders = ', '.join(['?' for _ in insert_values])
        query = f"INSERT INTO staff ({', '.join(insert_columns)}) VALUES ({placeholders})"
        
        print(f"Attempting to insert test staff...")
        print(f"Query: {query}")
        print(f"Values: {insert_values}")
        
        cursor.execute(query, insert_values)
        conn.commit()
        
        print("✅ Test staff insertion successful!")
        
        # Verify insertion
        cursor.execute("SELECT id, staff_id, full_name FROM staff WHERE staff_id = ?", (test_data['staff_id'],))
        result = cursor.fetchone()
        
        if result:
            print(f"✅ Verification successful: ID={result[0]}, Staff_ID={result[1]}, Name={result[2]}")
        else:
            print("❌ Verification failed: Staff not found after insertion")
        
        # Clean up
        cursor.execute("DELETE FROM staff WHERE staff_id = ?", (test_data['staff_id'],))
        conn.commit()
        print("🧹 Test data cleaned up")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during staff insertion test: {e}")
        return False

def main():
    print("=== Staff Creation Database Test ===\n")
    
    # Check schema
    required_columns = check_database_schema()
    
    if not required_columns:
        print("❌ Failed to check database schema")
        return
    
    print("\n" + "="*60 + "\n")
    
    # Test insertion
    success = test_staff_insertion()
    
    if success:
        print("\n✅ All tests passed! Staff creation should work properly.")
    else:
        print("\n❌ Tests failed! There are issues with staff creation.")

if __name__ == "__main__":
    main()
