#!/usr/bin/env python3
"""
Update the attendance table schema to support on-duty functionality
"""

import sqlite3
import os

def update_attendance_schema():
    """Add on-duty columns to the attendance table"""
    print("🔧 Updating Attendance Schema for On-Duty Functionality")
    print("=" * 60)
    
    if not os.path.exists('vishnorex.db'):
        print("❌ Database file 'vishnorex.db' not found!")
        return False
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Check current table structure
        cursor.execute("PRAGMA table_info(attendance)")
        columns = cursor.fetchall()
        existing_columns = [col[1] for col in columns]
        
        print("Current attendance table columns:")
        for col in existing_columns:
            print(f"  - {col}")
        
        # Check if new columns already exist
        new_columns = ['on_duty_type', 'on_duty_location', 'on_duty_purpose']
        columns_to_add = [col for col in new_columns if col not in existing_columns]
        
        if not columns_to_add:
            print("\n✅ All on-duty columns already exist!")
            return True
        
        print(f"\nAdding new columns: {columns_to_add}")
        
        # Add new columns
        for column in columns_to_add:
            try:
                cursor.execute(f'ALTER TABLE attendance ADD COLUMN {column} TEXT')
                print(f"✅ Added column: {column}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    print(f"⚠️  Column {column} already exists")
                else:
                    print(f"❌ Error adding column {column}: {e}")
                    return False
        
        # Update the status column constraint to include 'on_duty'
        print("\nUpdating status column constraint...")
        
        # Check current status values
        cursor.execute("SELECT DISTINCT status FROM attendance WHERE status IS NOT NULL")
        current_statuses = [row[0] for row in cursor.fetchall()]
        print(f"Current status values in database: {current_statuses}")
        
        # Note: SQLite doesn't support modifying CHECK constraints directly
        # The constraint will be enforced by the application logic
        print("✅ Status constraint will be enforced by application logic")
        
        conn.commit()
        
        # Verify the changes
        cursor.execute("PRAGMA table_info(attendance)")
        updated_columns = cursor.fetchall()
        
        print("\nUpdated attendance table structure:")
        for col in updated_columns:
            col_name, col_type, not_null, default, pk = col[1], col[2], col[3], col[4], col[5]
            nullable = "NOT NULL" if not_null else "NULL"
            primary = "PRIMARY KEY" if pk else ""
            default_val = f"DEFAULT {default}" if default else ""
            print(f"  - {col_name} ({col_type}) {nullable} {default_val} {primary}".strip())
        
        print("\n✅ Schema update completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error updating schema: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def test_on_duty_functionality():
    """Test the on-duty functionality with sample data"""
    print("\n🧪 Testing On-Duty Functionality")
    print("=" * 40)
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get a staff member for testing
        cursor.execute('SELECT id, full_name FROM staff LIMIT 1')
        staff = cursor.fetchone()
        
        if not staff:
            print("❌ No staff members found for testing")
            return False
        
        staff_id = staff['id']
        staff_name = staff['full_name']
        print(f"Testing with staff: {staff_name} (ID: {staff_id})")
        
        # Create a test on-duty attendance record
        test_date = '2025-07-20'
        
        # Check if record already exists
        cursor.execute('SELECT id FROM attendance WHERE staff_id = ? AND date = ?', (staff_id, test_date))
        existing = cursor.fetchone()
        
        if existing:
            # Update existing record
            cursor.execute('''
                UPDATE attendance 
                SET status = 'on_duty', 
                    on_duty_type = 'Training',
                    on_duty_location = 'Training Center',
                    on_duty_purpose = 'Professional development workshop'
                WHERE staff_id = ? AND date = ?
            ''', (staff_id, test_date))
            print(f"✅ Updated existing attendance record for {test_date}")
        else:
            # Create new record
            cursor.execute('''
                INSERT INTO attendance 
                (staff_id, school_id, date, status, on_duty_type, on_duty_location, on_duty_purpose)
                VALUES (?, 1, ?, 'on_duty', 'Training', 'Training Center', 'Professional development workshop')
            ''', (staff_id, test_date))
            print(f"✅ Created new on-duty attendance record for {test_date}")
        
        conn.commit()
        
        # Verify the record
        cursor.execute('''
            SELECT date, status, on_duty_type, on_duty_location, on_duty_purpose
            FROM attendance
            WHERE staff_id = ? AND date = ?
        ''', (staff_id, test_date))
        
        record = cursor.fetchone()
        if record:
            print(f"\n✅ Test record created successfully:")
            print(f"  - Date: {record['date']}")
            print(f"  - Status: {record['status']}")
            print(f"  - Duty Type: {record['on_duty_type']}")
            print(f"  - Location: {record['on_duty_location']}")
            print(f"  - Purpose: {record['on_duty_purpose']}")
        else:
            print("❌ Failed to create test record")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing functionality: {e}")
        return False
    finally:
        conn.close()

def verify_weekly_calendar_data():
    """Verify that the weekly calendar will display on-duty data correctly"""
    print("\n📅 Verifying Weekly Calendar Data")
    print("=" * 35)
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get on-duty attendance records
        cursor.execute('''
            SELECT s.full_name, a.date, a.status, a.on_duty_type, a.on_duty_location, a.on_duty_purpose
            FROM attendance a
            JOIN staff s ON a.staff_id = s.id
            WHERE a.status = 'on_duty'
            ORDER BY a.date DESC
            LIMIT 5
        ''')
        
        on_duty_records = cursor.fetchall()
        
        if on_duty_records:
            print(f"✅ Found {len(on_duty_records)} on-duty records:")
            for record in on_duty_records:
                print(f"  - {record['full_name']}: {record['on_duty_type']} on {record['date']}")
                print(f"    Location: {record['on_duty_location'] or 'Not specified'}")
                print(f"    Purpose: {record['on_duty_purpose'][:50]}...")
        else:
            print("⚠️  No on-duty records found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying calendar data: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("🚀 On-Duty Attendance Integration Setup")
    print("=" * 70)
    
    success = True
    
    # Update schema
    success &= update_attendance_schema()
    
    if success:
        # Test functionality
        success &= test_on_duty_functionality()
        
        # Verify calendar data
        success &= verify_weekly_calendar_data()
    
    if success:
        print("\n🎉 ON-DUTY INTEGRATION SETUP COMPLETE!")
        print("\nFeatures now available:")
        print("1. ✅ On-duty applications automatically mark attendance")
        print("2. ✅ Weekly calendar displays on-duty information")
        print("3. ✅ On-duty status shows duty type, location, and purpose")
        print("4. ✅ Database schema updated with new columns")
        
        print("\n📋 How it works:")
        print("1. Staff applies for on-duty through the application")
        print("2. Admin approves the on-duty application")
        print("3. System automatically marks attendance as 'On Duty'")
        print("4. Weekly calendar shows on-duty details instead of regular timing")
        print("5. On-duty days are counted as present for attendance statistics")
        
        print("\n🚀 Ready to use!")
    else:
        print("\n❌ Setup failed. Please check the errors above.")
