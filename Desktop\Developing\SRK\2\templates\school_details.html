<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>School Details - VishnoRex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">VishnoRex - {{ school.name }}</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('company_dashboard') }}">Back to Schools</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> Company Admin
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5>School Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Name:</strong> {{ school.name }}</p>
                                <p><strong>Address:</strong> {{ school.address }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Contact Email:</strong> {{ school.contact_email }}</p>
                                <p><strong>Contact Phone:</strong> {{ school.contact_phone }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Admin Section -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5>Admins</h5>
                        <button class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#addAdminModal">
                            <i class="bi bi-plus-lg"></i> Add Admin
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Username</th>
                                        <th>Full Name</th>
                                        <th>Email</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for admin in admins %}
                                    <tr>
                                        <td>{{ admin.username }}</td>
                                        <td>{{ admin.full_name }}</td>
                                        <td>{{ admin.email }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-danger delete-admin" data-admin-id="{{ admin.id }}">
                                                <i class="bi bi-trash"></i> Delete
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Add Admin Modal -->
                <div class="modal fade" id="addAdminModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title">Add New Admin</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="adminForm">
                                    <div class="mb-3">
                                        <label for="newAdminUsername" class="form-label">Username</label>
                                        <input type="text" class="form-control" id="newAdminUsername" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="newAdminPassword" class="form-label">Password</label>
                                        <input type="password" class="form-control" id="newAdminPassword" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="newAdminFullName" class="form-label">Full Name</label>
                                        <input type="text" class="form-control" id="newAdminFullName" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="newAdminEmail" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="newAdminEmail">
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" id="saveAdmin">Save</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Staff Section -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5>Staff Members ({{ staff|length }})</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Staff ID</th>
                                        <th>Full Name</th>
                                        <th>Department</th>
                                        <th>Position</th>
                                        <th>Email</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for member in staff %}
                                    <tr>
                                        <td>{{ member.staff_id }}</td>
                                        <td>{{ member.full_name }}</td>
                                        <td>{{ member.department }}</td>
                                        <td>{{ member.position }}</td>
                                        <td>{{ member.email }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Today's Attendance -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5>Today's Attendance ({{ today.strftime('%d %b %Y') }})</h5>
                        <div>
                            <span class="badge bg-success">{{ attendance_summary.present }} Present</span>
                            <span class="badge bg-danger ms-2">{{ attendance_summary.absent }} Absent</span>
                            <span class="badge bg-warning ms-2">{{ attendance_summary.late }} Late</span>
                            <span class="badge bg-info ms-2">{{ attendance_summary.on_leave }} On Leave</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Similar to admin dashboard attendance view -->
                    </div>
                </div>

                <!-- Pending Leaves -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5>Pending Leave Applications</h5>
                    </div>
                    <div class="card-body">
                        <!-- Similar to admin dashboard pending leaves view -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add admin
        document.getElementById('saveAdmin')?.addEventListener('click', function() {
            const username = document.getElementById('newAdminUsername').value;
            const password = document.getElementById('newAdminPassword').value;
            const fullName = document.getElementById('newAdminFullName').value;
            const email = document.getElementById('newAdminEmail').value;

            if (!username || !password || !fullName) {
                alert('Username, Password, and Full Name are required');
                return;
            }

            fetch('/add_admin', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `school_id={{ school.id }}&username=${username}&password=${encodeURIComponent(password)}&full_name=${encodeURIComponent(fullName)}&email=${email}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Admin added successfully');
                    location.reload();
                } else {
                    alert(data.error || 'Failed to add admin');
                }
            });
        });

        // Delete admin
        document.querySelectorAll('.delete-admin').forEach(btn => {
            btn.addEventListener('click', function() {
                const adminId = this.getAttribute('data-admin-id');
                const adminName = this.closest('tr').querySelector('td:nth-child(2)').textContent;

                if (confirm(`Are you sure you want to delete admin ${adminName}?`)) {
                    fetch('/delete_admin', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `admin_id=${adminId}`
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('Admin deleted successfully');
                            location.reload();
                        } else {
                            alert(data.error || 'Failed to delete admin');
                        }
                    });
                }
            });
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
