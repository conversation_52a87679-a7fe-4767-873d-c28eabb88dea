#!/usr/bin/env python3
"""
Test sync for specific staff member (888 - <PERSON>)
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from zk_biometric import ZKBiometricDevice
import sqlite3
from datetime import datetime, <PERSON><PERSON><PERSON>

def test_sync_for_staff_888():
    """Test sync specifically for staff ID 888 (<PERSON>)"""
    
    device_ip = '*************'
    target_staff_id = '888'
    
    print(f"=== Testing Sync for Staff ID {target_staff_id} ===\n")
    
    try:
        # Connect to device
        zk_device = ZKBiometricDevice(device_ip)
        if not zk_device.connect():
            print("❌ Failed to connect to biometric device")
            return
        
        print("✅ Connected to biometric device")
        
        # Get all attendance records
        all_records = zk_device.get_attendance_records()
        print(f"📊 Total records on device: {len(all_records)}")
        
        # Filter records for staff 888
        staff_888_records = [r for r in all_records if str(r.get('user_id')) == target_staff_id]
        print(f"📊 Records for staff {target_staff_id}: {len(staff_888_records)}")
        
        if staff_888_records:
            print("\n🔍 Recent records for staff 888:")
            # Sort by timestamp and show recent ones
            staff_888_records.sort(key=lambda x: x.get('timestamp'), reverse=True)
            
            for i, record in enumerate(staff_888_records[:10]):  # Show last 10
                timestamp = record.get('timestamp')
                verification_type = record.get('verification_type', 'unknown')
                print(f"   {i+1}. {timestamp} - {verification_type}")
        
        # Get records from last 7 days
        week_ago = datetime.now() - timedelta(days=7)
        recent_records = [r for r in staff_888_records 
                         if r.get('timestamp') and r.get('timestamp') >= week_ago]
        
        print(f"\n📅 Records from last 7 days: {len(recent_records)}")
        
        zk_device.disconnect()
        
        # Now test the sync process
        if recent_records:
            print("\n🔄 Testing sync process...")
            test_manual_sync(recent_records, target_staff_id)
        else:
            print("\n⚠️ No recent records to sync")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_manual_sync(records, staff_id):
    """Manually test the sync process"""
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get staff database ID
        cursor.execute('SELECT id, school_id FROM staff WHERE staff_id = ?', (staff_id,))
        staff_record = cursor.fetchone()
        
        if not staff_record:
            print(f"❌ Staff {staff_id} not found in database")
            return
        
        staff_db_id = staff_record['id']
        school_id = staff_record['school_id']
        
        print(f"✅ Staff found - DB ID: {staff_db_id}, School ID: {school_id}")
        
        synced_count = 0
        
        for record in records:
            try:
                attendance_date = record['timestamp'].date()
                attendance_time = record['timestamp'].strftime('%H:%M:%S')
                verification_type = record.get('verification_type', 'check-in')
                
                print(f"\n📝 Processing: {attendance_date} {attendance_time} - {verification_type}")
                
                # Check if attendance record exists
                cursor.execute('''
                    SELECT * FROM attendance WHERE staff_id = ? AND date = ?
                ''', (staff_db_id, attendance_date))
                existing_record = cursor.fetchone()
                
                if existing_record:
                    print(f"   📋 Existing record found")
                    # Update based on verification type
                    if verification_type == 'check-in' and not existing_record['time_in']:
                        cursor.execute('''
                            UPDATE attendance SET time_in = ?, status = 'present'
                            WHERE staff_id = ? AND date = ?
                        ''', (attendance_time, staff_db_id, attendance_date))
                        print(f"   ✅ Updated check-in time")
                        synced_count += 1
                    elif verification_type == 'check-out' and not existing_record['time_out']:
                        cursor.execute('''
                            UPDATE attendance SET time_out = ?
                            WHERE staff_id = ? AND date = ?
                        ''', (attendance_time, staff_db_id, attendance_date))
                        print(f"   ✅ Updated check-out time")
                        synced_count += 1
                    else:
                        print(f"   ⏭️ Skipped (already has {verification_type})")
                else:
                    print(f"   📝 Creating new record")
                    if verification_type == 'check-in':
                        cursor.execute('''
                            INSERT INTO attendance (staff_id, school_id, date, time_in, status)
                            VALUES (?, ?, ?, ?, 'present')
                        ''', (staff_db_id, school_id, attendance_date, attendance_time))
                        print(f"   ✅ Created new attendance record")
                        synced_count += 1
                        
            except Exception as record_error:
                print(f"   ❌ Error processing record: {record_error}")
                continue
        
        # Commit changes
        conn.commit()
        print(f"\n🎉 Sync completed! Processed {synced_count} records")
        
        # Show updated attendance
        print("\n📊 Updated attendance records:")
        cursor.execute('''
            SELECT date, time_in, time_out, status 
            FROM attendance 
            WHERE staff_id = ? 
            ORDER BY date DESC 
            LIMIT 5
        ''', (staff_db_id,))
        
        updated_records = cursor.fetchall()
        for record in updated_records:
            print(f"   {record['date']} - In: {record['time_in']}, Out: {record['time_out']}, Status: {record['status']}")
        
    except Exception as e:
        print(f"❌ Sync error: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    test_sync_for_staff_888()
