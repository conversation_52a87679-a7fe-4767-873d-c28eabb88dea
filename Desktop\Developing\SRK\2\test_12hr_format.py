#!/usr/bin/env python3
"""
12-Hour Format Conversion Test Suite

This script tests the conversion of all 24-hour time formats to 12-hour format
throughout the VishnoRex attendance management system.
"""

import sqlite3
from datetime import datetime, date, time
import sys


def test_time_formatting():
    """Test the time formatting function"""
    print("🕐 Testing Time Formatting Functions")
    print("=" * 50)
    
    # Test cases for 24-hour to 12-hour conversion
    test_cases = [
        # (24-hour input, expected 12-hour output)
        ('00:00:00', '12:00 AM'),
        ('00:30:00', '12:30 AM'),
        ('01:00:00', '01:00 AM'),
        ('09:20:00', '09:20 AM'),
        ('09:30:00', '09:30 AM'),
        ('12:00:00', '12:00 PM'),
        ('12:30:00', '12:30 PM'),
        ('13:00:00', '01:00 PM'),
        ('16:30:00', '04:30 PM'),
        ('17:00:00', '05:00 PM'),
        ('23:59:00', '11:59 PM'),
        # Test HH:MM format (without seconds)
        ('09:20', '09:20 AM'),
        ('16:30', '04:30 PM'),
        ('00:00', '12:00 AM'),
        ('12:00', '12:00 PM'),
    ]
    
    def format_time_to_12hr(time_str):
        """Local implementation of the format_time_to_12hr function"""
        if not time_str:
            return None
        
        try:
            # Handle both HH:MM:SS and HH:MM formats
            if len(time_str.split(':')) == 3:
                time_obj = datetime.strptime(time_str, '%H:%M:%S').time()
            else:
                time_obj = datetime.strptime(time_str, '%H:%M').time()
            
            # Convert to 12-hour format
            return datetime.combine(date.today(), time_obj).strftime('%I:%M %p')
        except:
            return time_str  # Return original if conversion fails
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for time_24hr, expected_12hr in test_cases:
        actual_12hr = format_time_to_12hr(time_24hr)
        
        if actual_12hr == expected_12hr:
            print(f"✅ {time_24hr} → {actual_12hr}")
            passed_tests += 1
        else:
            print(f"❌ {time_24hr} → Expected: {expected_12hr}, Got: {actual_12hr}")
    
    print(f"\n📊 Time Formatting Results: {passed_tests}/{total_tests} tests passed")
    return passed_tests == total_tests


def test_duration_formatting():
    """Test duration formatting"""
    print("\n⏱️  Testing Duration Formatting")
    print("=" * 50)
    
    def format_duration_minutes(minutes):
        """Local implementation of duration formatting"""
        if minutes <= 0:
            return ""
        
        hours = minutes // 60
        mins = minutes % 60
        
        if hours > 0:
            if mins > 0:
                return f"{hours}h {mins}m"
            else:
                return f"{hours}h"
        else:
            return f"{mins}m"
    
    test_cases = [
        (5, "5m"),
        (15, "15m"),
        (30, "30m"),
        (60, "1h"),
        (75, "1h 15m"),
        (90, "1h 30m"),
        (120, "2h"),
        (135, "2h 15m"),
        (180, "3h"),
    ]
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for minutes, expected in test_cases:
        actual = format_duration_minutes(minutes)
        
        if actual == expected:
            print(f"✅ {minutes} minutes → {actual}")
            passed_tests += 1
        else:
            print(f"❌ {minutes} minutes → Expected: {expected}, Got: {actual}")
    
    print(f"\n📊 Duration Formatting Results: {passed_tests}/{total_tests} tests passed")
    return passed_tests == total_tests


def test_database_time_fields():
    """Test that database has the necessary time fields"""
    print("\n🗄️  Testing Database Time Fields")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('vishnorex.db')
        cursor = conn.cursor()
        
        # Test attendance table structure
        cursor.execute("PRAGMA table_info(attendance)")
        attendance_columns = [col[1] for col in cursor.fetchall()]
        
        required_time_fields = [
            'time_in', 'time_out', 'overtime_in', 'overtime_out',
            'shift_start_time', 'shift_end_time'
        ]
        
        missing_fields = [field for field in required_time_fields if field not in attendance_columns]
        
        if not missing_fields:
            print("✅ All required time fields exist in attendance table")
            for field in required_time_fields:
                print(f"   - {field}")
        else:
            print(f"❌ Missing time fields: {missing_fields}")
            return False
        
        # Test shift_definitions table
        cursor.execute("PRAGMA table_info(shift_definitions)")
        shift_columns = [col[1] for col in cursor.fetchall()]
        
        shift_time_fields = ['start_time', 'end_time']
        missing_shift_fields = [field for field in shift_time_fields if field not in shift_columns]
        
        if not missing_shift_fields:
            print("✅ All required time fields exist in shift_definitions table")
            for field in shift_time_fields:
                print(f"   - {field}")
        else:
            print(f"❌ Missing shift time fields: {missing_shift_fields}")
            return False
        
        # Test sample data format
        cursor.execute("SELECT start_time, end_time FROM shift_definitions WHERE is_active = 1 LIMIT 1")
        sample_shift = cursor.fetchone()
        
        if sample_shift:
            print(f"✅ Sample shift data: {sample_shift[0]} - {sample_shift[1]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False


def test_template_filters():
    """Test template filter behavior"""
    print("\n🎨 Testing Template Filter Behavior")
    print("=" * 50)
    
    # Simulate template filter behavior
    def timeformat_filter(time_input, format='%I:%M %p'):
        """Simulate the timeformat template filter"""
        if time_input is None:
            return "--:--"
        
        if isinstance(time_input, str):
            try:
                # Try to parse string time
                time_obj = datetime.strptime(time_input, '%H:%M:%S').time()
            except ValueError:
                try:
                    # Try alternative format
                    time_obj = datetime.strptime(time_input, '%H:%M').time()
                except ValueError:
                    return time_input  # Return as-is if can't parse
        else:
            time_obj = time_input
        
        # Convert to 12-hour format
        return datetime.combine(date.today(), time_obj).strftime(format)
    
    test_cases = [
        ('09:20:00', '09:20 AM'),
        ('16:30:00', '04:30 PM'),
        ('00:00:00', '12:00 AM'),
        ('12:00:00', '12:00 PM'),
        (None, '--:--'),
        ('invalid', 'invalid'),
    ]
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for input_time, expected in test_cases:
        actual = timeformat_filter(input_time)
        
        if actual == expected:
            print(f"✅ timeformat_filter('{input_time}') → {actual}")
            passed_tests += 1
        else:
            print(f"❌ timeformat_filter('{input_time}') → Expected: {expected}, Got: {actual}")
    
    print(f"\n📊 Template Filter Results: {passed_tests}/{total_tests} tests passed")
    return passed_tests == total_tests


def main():
    """Run all tests"""
    print("🚀 Starting 12-Hour Format Conversion Tests")
    print("=" * 60)
    
    test_results = []
    
    # Run all tests
    test_results.append(test_time_formatting())
    test_results.append(test_duration_formatting())
    test_results.append(test_database_time_fields())
    test_results.append(test_template_filters())
    
    # Summary
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print("\n" + "=" * 60)
    print("📊 OVERALL TEST SUMMARY")
    print("=" * 60)
    
    test_names = [
        "Time Formatting Functions",
        "Duration Formatting",
        "Database Time Fields",
        "Template Filters"
    ]
    
    for i, (name, passed) in enumerate(zip(test_names, test_results)):
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status}: {name}")
    
    print(f"\nTotal: {passed_tests}/{total_tests} test suites passed")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 All 12-hour format conversion tests passed!")
        print("✅ The system is ready to display all times in 12-hour format")
        return True
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
