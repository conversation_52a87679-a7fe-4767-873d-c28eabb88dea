#!/usr/bin/env python3
"""
Fix Authentication Issues
Try different authentication parameters to connect to the device
"""

from zk_biometric import ZKBiometricDevice
import time

def test_authentication_combinations():
    """Test different authentication combinations"""
    print("🔐 TESTING AUTHENTICATION COMBINATIONS")
    print("=" * 60)
    
    # Different authentication combinations to try
    auth_combinations = [
        {"device_id": "1", "timeout": 30, "description": "Device ID 1, Long timeout"},
        {"device_id": "0", "timeout": 30, "description": "Device ID 0, Long timeout"},
        {"device_id": None, "timeout": 30, "description": "No Device ID, Long timeout"},
        {"device_id": "181", "timeout": 30, "description": "Device ID 181, Long timeout"},
        {"device_id": "1", "timeout": 5, "description": "Device ID 1, Short timeout"},
    ]
    
    device_ip = "*************"
    port = 4370
    
    for i, auth in enumerate(auth_combinations, 1):
        print(f"\n🧪 Test {i}/{len(auth_combinations)}: {auth['description']}")
        print(f"   IP: {device_ip}, Port: {port}")
        print(f"   Device ID: {auth['device_id']}, Timeout: {auth['timeout']}")
        
        try:
            device = ZKBiometricDevice(
                device_ip=device_ip,
                port=port,
                timeout=auth['timeout'],
                device_id=auth['device_id'],
                use_cloud=False
            )
            
            print("   Attempting connection...")
            if device.connect():
                print("   🎉 SUCCESS! Authentication working")
                
                try:
                    users = device.get_users()
                    print(f"   📱 Found {len(users)} users")
                    
                    device.disconnect()
                    print("   ✅ Disconnected successfully")
                    
                    return auth
                    
                except Exception as e:
                    print(f"   ⚠️ Connected but error: {e}")
                    device.disconnect()
                    return auth  # Still successful connection
                    
            else:
                print("   ❌ Connection failed")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return None

def test_web_interface_access():
    """Test web interface access for configuration"""
    print("\n🌐 TESTING WEB INTERFACE ACCESS")
    print("-" * 40)
    
    import requests
    
    urls_to_test = [
        "http://*************",
        "http://*************:80",
        "http://*************:8080",
        "http://182.66.109.42",
        "http://182.66.109.42:80"
    ]
    
    for url in urls_to_test:
        try:
            print(f"Testing {url}...", end=" ")
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print("✅ ACCESSIBLE")
                
                # Check if it looks like a ZK device interface
                content = response.text.lower()
                if any(keyword in content for keyword in ['zkteco', 'attendance', 'biometric', 'login']):
                    print(f"   🎯 Appears to be ZK device interface")
                    return url
                else:
                    print(f"   ⚠️ Accessible but may not be ZK device")
            else:
                print(f"❌ HTTP {response.status_code}")
                
        except requests.exceptions.ConnectTimeout:
            print("❌ Timeout")
        except requests.exceptions.ConnectionError:
            print("❌ Connection refused")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return None

def update_app_with_working_auth(auth_config):
    """Update app.py with working authentication"""
    print(f"\n📝 UPDATING APP WITH WORKING AUTHENTICATION")
    print("-" * 50)
    
    if not auth_config:
        print("❌ No working authentication to apply")
        return False
    
    print(f"Working authentication:")
    print(f"   Device ID: {auth_config['device_id']}")
    print(f"   Timeout: {auth_config['timeout']}")
    
    try:
        # Read app.py
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update the ZKBiometricDevice initialization in test_biometric_connection
        import re
        
        # Find and replace the ZKBiometricDevice initialization
        old_pattern = r'zk_device = ZKBiometricDevice\(device_ip, port=port, timeout=15, device_id=\'1\', use_cloud=False\)'
        new_replacement = f'zk_device = ZKBiometricDevice(device_ip, port=port, timeout={auth_config["timeout"]}, device_id=\'{auth_config["device_id"]}\', use_cloud=False)'
        
        content = re.sub(old_pattern, new_replacement, content)
        
        # Save updated app.py
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ App.py updated with working authentication")
        return True
        
    except Exception as e:
        print(f"❌ Error updating app: {e}")
        return False

def provide_manual_configuration_guide():
    """Provide manual configuration guide"""
    print(f"\n📋 MANUAL CONFIGURATION GUIDE")
    print("=" * 50)
    
    print("If authentication still fails, configure the device manually:")
    print()
    
    print("🌐 WEB INTERFACE METHOD:")
    print("1. Open browser: http://*************")
    print("2. Login with admin credentials:")
    print("   - admin / 123456")
    print("   - admin / admin")
    print("   - administrator / 123456")
    print("3. Find Communication/Network settings")
    print("4. Configure:")
    print("   - TCP/IP Communication: ENABLE")
    print("   - Device ID: 1")
    print("   - Common Key: 0")
    print("   - Connection Mode: PC Connection")
    print("5. Save and restart device")
    print()
    
    print("📱 DEVICE MENU METHOD (if device has display):")
    print("1. Access device menu via keypad")
    print("2. Navigate to System → Communication")
    print("3. Enable TCP/IP communication")
    print("4. Set Device ID and authentication settings")
    print()
    
    print("🔄 FACTORY RESET METHOD:")
    print("1. Power off device")
    print("2. Hold RESET button while powering on")
    print("3. Keep holding for 15 seconds")
    print("4. Device will reset to factory defaults")

def main():
    """Main authentication fix function"""
    print("🔐 AUTHENTICATION FIX TOOL")
    print("=" * 60)
    print("Fixing 'Unauthenticated' error for ZK device connection")
    print("=" * 60)
    
    # Test different authentication combinations
    working_auth = test_authentication_combinations()
    
    if working_auth:
        print(f"\n🎉 SUCCESS! Found working authentication:")
        print(f"   Device ID: {working_auth['device_id']}")
        print(f"   Timeout: {working_auth['timeout']}")
        
        # Update app with working authentication
        if update_app_with_working_auth(working_auth):
            print("✅ App updated with working authentication")
        
        print(f"\n📋 NEXT STEPS:")
        print("1. ✅ App.py has been updated")
        print("2. 🔄 Restart your Flask application")
        print("3. 🧪 Test biometric connection")
        print("4. 🎉 Connection should now work!")
        
    else:
        print(f"\n❌ No working authentication found")
        
        # Test web interface access
        web_url = test_web_interface_access()
        
        if web_url:
            print(f"\n✅ Web interface accessible at: {web_url}")
            print("Use web interface to configure device authentication")
        
        # Provide manual configuration guide
        provide_manual_configuration_guide()

if __name__ == '__main__':
    main()
