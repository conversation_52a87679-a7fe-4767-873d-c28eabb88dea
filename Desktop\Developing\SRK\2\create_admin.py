from werkzeug.security import generate_password_hash
from database import get_db, init_db
from app import app  # Import your Flask app

def create_company_admin():
    with app.app_context():
        db = get_db()
        try:
            db.execute('''
                INSERT INTO company_admins (username, password, full_name)
                VALUES (?, ?, ?)
            ''', ('admin', generate_password_hash('admin123'), 'Admin User'))
            db.commit()
            print("Company admin created successfully!")
        except sqlite3.IntegrityError:
            print("Admin already exists")
        except Exception as e:
            print(f"Error creating admin: {str(e)}")

if __name__ == '__main__':
    create_company_admin()