#!/usr/bin/env python3
"""
Test the admin modal functionality by simulating the AJAX request
"""

import sqlite3
import datetime
import json
import sys
import os

# Add current directory to path to import app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_get_comprehensive_staff_profile_route():
    """Test the get_comprehensive_staff_profile route with actual data"""
    print("=== Testing get_comprehensive_staff_profile Route ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get all staff members
        cursor.execute('SELECT id, staff_id, full_name FROM staff')
        all_staff = cursor.fetchall()
        
        print(f"Found {len(all_staff)} staff members:")
        for staff in all_staff:
            print(f"  - ID: {staff['id']}, Staff Number: {staff['staff_id']}, Name: {staff['full_name']}")
        
        if not all_staff:
            print("❌ No staff members found!")
            return False
        
        # Test the route for each staff member
        for staff_member in all_staff:
            staff_id = staff_member['id']
            print(f"\n--- Testing Staff ID {staff_id} ({staff_member['full_name']}) ---")
            
            # Simulate the route logic
            # Get staff information
            staff = cursor.execute('''
                SELECT s.*, sc.name as school_name
                FROM staff s
                LEFT JOIN schools sc ON s.school_id = sc.id
                WHERE s.id = ?
            ''', (staff_id,)).fetchone()
            
            if not staff:
                print(f"❌ Staff {staff_id} not found")
                continue
                
            print(f"✅ Staff info retrieved: {staff['full_name']}")
            
            # Get attendance records (last 30 days)
            thirty_days_ago = (datetime.datetime.now() - datetime.timedelta(days=30)).date()
            attendance = cursor.execute('''
                SELECT date, time_in, time_out, overtime_in, overtime_out, status
                FROM attendance
                WHERE staff_id = ? AND date >= ?
                ORDER BY date DESC
            ''', (staff_id, thirty_days_ago)).fetchall()
            
            print(f"  - Attendance records: {len(attendance)}")
            
            # Get biometric verifications (last 30 days)
            verifications = cursor.execute('''
                SELECT verification_type, verification_time, verification_status, device_ip
                FROM biometric_verifications
                WHERE staff_id = ? AND DATE(verification_time) >= ?
                ORDER BY verification_time DESC
                LIMIT 50
            ''', (staff_id, thirty_days_ago)).fetchall()
            
            print(f"  - Biometric verifications: {len(verifications)}")
            
            # Show verification details if any
            if verifications:
                print("  - Recent verifications:")
                for i, v in enumerate(verifications[:3]):
                    print(f"    {i+1}. {v['verification_time']}: {v['verification_type']} - {v['verification_status']}")
            else:
                print("  - ❌ No verifications found!")
            
            # Get leave applications
            leaves = cursor.execute('''
                SELECT leave_type, start_date, end_date, reason, status, applied_at
                FROM leave_applications
                WHERE staff_id = ?
                ORDER BY applied_at DESC
                LIMIT 20
            ''', (staff_id,)).fetchall()
            
            print(f"  - Leave applications: {len(leaves)}")
            
            # Calculate attendance statistics
            total_days = len(attendance)
            present_days = len([a for a in attendance if a['status'] in ['present', 'late']])
            absent_days = len([a for a in attendance if a['status'] == 'absent'])
            late_days = len([a for a in attendance if a['status'] == 'late'])
            
            attendance_stats = {
                'total_days': total_days,
                'present_days': present_days,
                'absent_days': absent_days,
                'late_days': late_days,
                'attendance_percentage': round((present_days / total_days * 100) if total_days > 0 else 0, 1)
            }
            
            print(f"  - Attendance stats: {attendance_stats}")
            
            # Simulate the JSON response
            response_data = {
                'success': True,
                'staff': dict(staff),
                'attendance': [dict(a) for a in attendance],
                'verifications': [dict(v) for v in verifications],
                'leaves': [dict(l) for l in leaves],
                'attendance_stats': attendance_stats
            }
            
            # Check if the response would be valid
            if response_data['success'] and len(response_data['verifications']) > 0:
                print(f"  ✅ Route would return valid data with {len(response_data['verifications'])} verifications")
            elif response_data['success'] and len(response_data['verifications']) == 0:
                print(f"  ⚠️  Route would return valid data but NO verifications")
            else:
                print(f"  ❌ Route would fail")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing route: {e}")
        return False
    finally:
        conn.close()

def check_javascript_modal_structure():
    """Check if the JavaScript modal structure is correct"""
    print("\n=== Checking JavaScript Modal Structure ===")
    
    # Check admin_dashboard.js
    admin_js_path = 'static/js/admin_dashboard.js'
    if os.path.exists(admin_js_path):
        print(f"✅ {admin_js_path} exists")
        
        with open(admin_js_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for key functions
        functions_to_check = [
            'loadComprehensiveStaffProfile',
            'renderComprehensiveStaffProfile',
            'setupStaffProfileButtons'
        ]
        
        for func in functions_to_check:
            if func in content:
                print(f"  ✅ {func} function found")
            else:
                print(f"  ❌ {func} function NOT found")
        
        # Check for modal elements
        modal_elements = [
            'staffProfileModal',
            'staffProfileModalContent',
            'staffProfileModalTitle'
        ]
        
        for element in modal_elements:
            if element in content:
                print(f"  ✅ {element} element referenced")
            else:
                print(f"  ❌ {element} element NOT referenced")
    else:
        print(f"❌ {admin_js_path} does not exist")
    
    # Check admin_dashboard.html
    admin_html_path = 'templates/admin_dashboard.html'
    if os.path.exists(admin_html_path):
        print(f"✅ {admin_html_path} exists")
        
        with open(admin_html_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for modal structure
        if 'id="staffProfileModal"' in content:
            print(f"  ✅ staffProfileModal div found")
        else:
            print(f"  ❌ staffProfileModal div NOT found")
            
        if 'id="staffProfileModalContent"' in content:
            print(f"  ✅ staffProfileModalContent div found")
        else:
            print(f"  ❌ staffProfileModalContent div NOT found")
    else:
        print(f"❌ {admin_html_path} does not exist")

if __name__ == "__main__":
    print("🔍 Testing Admin Modal Functionality")
    print("=" * 50)
    
    success = test_get_comprehensive_staff_profile_route()
    check_javascript_modal_structure()
    
    if success:
        print("\n✅ Admin modal functionality test completed")
        print("\nIf the modal is still not working, the issue might be:")
        print("1. JavaScript errors in the browser console")
        print("2. Bootstrap modal initialization issues")
        print("3. AJAX request failures")
        print("4. Session/authentication issues")
    else:
        print("\n❌ Admin modal functionality test failed")
