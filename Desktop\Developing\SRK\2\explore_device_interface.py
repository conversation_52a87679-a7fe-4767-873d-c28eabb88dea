#!/usr/bin/env python3
"""
Explore ZK Device Web Interface
Map out the device's web interface to find configuration options
"""

import requests
import re
from urllib.parse import urljoin, urlparse
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class DeviceExplorer:
    """Explore ZK device web interface"""
    
    def __init__(self, ip="*************"):
        self.ip = ip
        self.base_url = f"http://{ip}"
        self.session = requests.Session()
        self.session.verify = False
        self.visited_urls = set()
        self.found_pages = {}
        
    def login(self, username="admin", password="123456"):
        """Login to device"""
        print(f"🔐 Logging into device...")
        
        try:
            response = self.session.get(self.base_url, timeout=10)
            
            login_data = {
                "username": username,
                "password": password,
                "user": username,
                "pass": password,
                "login": "Login",
                "submit": "Login"
            }
            
            login_response = self.session.post(self.base_url, data=login_data, timeout=10)
            
            if login_response.status_code == 200:
                content = login_response.text.lower()
                if any(success in content for success in ['main', 'system', 'device', 'dashboard', 'menu']):
                    print(f"✅ Successfully logged in")
                    return True
            
            print(f"❌ Login failed")
            return False
                
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False
    
    def extract_links(self, html_content, base_url):
        """Extract links from HTML content"""
        links = set()

        # Use regex to find links and form actions
        href_pattern = r'(?:href|action)=["\']([^"\']*)["\']'
        matches = re.findall(href_pattern, html_content, re.IGNORECASE)

        for match in matches:
            if match and not match.startswith('#') and not match.startswith('javascript:'):
                full_url = urljoin(base_url, match)
                links.add(full_url)

        return links
    
    def categorize_page(self, url, content):
        """Categorize page based on content"""
        content_lower = content.lower()
        
        categories = []
        
        # Communication/Network settings
        if any(keyword in content_lower for keyword in ['tcp', 'communication', 'network', 'device id', 'common key']):
            categories.append('communication')
        
        # System settings
        if any(keyword in content_lower for keyword in ['system', 'settings', 'configuration', 'admin']):
            categories.append('system')
        
        # Device management
        if any(keyword in content_lower for keyword in ['device', 'biometric', 'fingerprint', 'attendance']):
            categories.append('device')
        
        # User management
        if any(keyword in content_lower for keyword in ['user', 'employee', 'staff', 'enroll']):
            categories.append('users')
        
        # Reports
        if any(keyword in content_lower for keyword in ['report', 'log', 'record', 'attendance']):
            categories.append('reports')
        
        return categories if categories else ['unknown']
    
    def explore_page(self, url, depth=0, max_depth=2):
        """Explore a single page"""
        if depth > max_depth or url in self.visited_urls:
            return
        
        self.visited_urls.add(url)
        
        try:
            print(f"{'  ' * depth}🔍 Exploring: {url}")
            response = self.session.get(url, timeout=5)
            
            if response.status_code == 200:
                content = response.text
                categories = self.categorize_page(url, content)
                
                self.found_pages[url] = {
                    'categories': categories,
                    'title': self.extract_title(content),
                    'forms': self.extract_forms(content),
                    'size': len(content)
                }
                
                print(f"{'  ' * depth}   ✅ Categories: {', '.join(categories)}")
                
                # Extract and explore links
                if depth < max_depth:
                    links = self.extract_links(content, url)
                    for link in links:
                        if self.is_internal_link(link):
                            self.explore_page(link, depth + 1, max_depth)
            
            else:
                print(f"{'  ' * depth}   ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"{'  ' * depth}   ❌ Error: {e}")
    
    def extract_title(self, content):
        """Extract page title"""
        title_match = re.search(r'<title[^>]*>([^<]*)</title>', content, re.IGNORECASE)
        if title_match:
            return title_match.group(1).strip()
        return "No title"
    
    def extract_forms(self, content):
        """Extract form information"""
        forms = []

        # Find forms using regex
        form_pattern = r'<form[^>]*>(.*?)</form>'
        form_matches = re.findall(form_pattern, content, re.IGNORECASE | re.DOTALL)

        for form_content in form_matches:
            # Extract form attributes
            action_match = re.search(r'action=["\']([^"\']*)["\']', form_content, re.IGNORECASE)
            method_match = re.search(r'method=["\']([^"\']*)["\']', form_content, re.IGNORECASE)

            form_info = {
                'action': action_match.group(1) if action_match else '',
                'method': method_match.group(1) if method_match else 'GET',
                'fields': []
            }

            # Extract input fields
            input_pattern = r'<input[^>]*name=["\']([^"\']*)["\'][^>]*(?:type=["\']([^"\']*)["\'])?[^>]*(?:value=["\']([^"\']*)["\'])?[^>]*>'
            input_matches = re.findall(input_pattern, form_content, re.IGNORECASE)

            for name, field_type, value in input_matches:
                if name:
                    form_info['fields'].append({
                        'name': name,
                        'type': field_type or 'text',
                        'value': value or ''
                    })

            # Extract select fields
            select_pattern = r'<select[^>]*name=["\']([^"\']*)["\'][^>]*>'
            select_matches = re.findall(select_pattern, form_content, re.IGNORECASE)

            for name in select_matches:
                if name:
                    form_info['fields'].append({
                        'name': name,
                        'type': 'select',
                        'value': ''
                    })

            if form_info['fields']:
                forms.append(form_info)

        return forms
    
    def is_internal_link(self, url):
        """Check if link is internal to the device"""
        parsed = urlparse(url)
        return parsed.netloc == self.ip or parsed.netloc == ''
    
    def print_summary(self):
        """Print exploration summary"""
        print(f"\n📊 DEVICE INTERFACE EXPLORATION SUMMARY")
        print("=" * 60)
        
        # Group pages by category
        by_category = {}
        for url, info in self.found_pages.items():
            for category in info['categories']:
                if category not in by_category:
                    by_category[category] = []
                by_category[category].append((url, info))
        
        # Print by category
        for category, pages in by_category.items():
            print(f"\n📁 {category.upper()} PAGES:")
            print("-" * 40)
            
            for url, info in pages:
                print(f"   🔗 {url}")
                print(f"      Title: {info['title']}")
                print(f"      Forms: {len(info['forms'])}")
                
                # Show form details for communication pages
                if category == 'communication' and info['forms']:
                    for i, form in enumerate(info['forms']):
                        print(f"         Form {i+1}: {len(form['fields'])} fields")
                        for field in form['fields'][:5]:  # Show first 5 fields
                            print(f"            - {field['name']} ({field['type']})")
        
        # Find most promising configuration pages
        print(f"\n🎯 MOST PROMISING CONFIGURATION PAGES:")
        print("-" * 40)
        
        comm_pages = by_category.get('communication', [])
        system_pages = by_category.get('system', [])
        
        promising = []
        
        for url, info in comm_pages + system_pages:
            score = 0
            content_keywords = ['device id', 'common key', 'tcp', 'port', 'communication']
            
            # Score based on forms and likely keywords
            score += len(info['forms']) * 10
            
            # Check if URL suggests configuration
            if any(keyword in url.lower() for keyword in ['comm', 'config', 'system', 'network']):
                score += 20
            
            promising.append((score, url, info))
        
        # Sort by score and show top candidates
        promising.sort(reverse=True)
        
        for score, url, info in promising[:5]:
            print(f"   🌟 Score {score}: {url}")
            print(f"      {info['title']}")
            if info['forms']:
                print(f"      {len(info['forms'])} forms with {sum(len(f['fields']) for f in info['forms'])} total fields")

def main():
    """Main exploration function"""
    print("🗺️ ZK DEVICE INTERFACE EXPLORER")
    print("=" * 60)
    print("Device: *************")
    print("Mapping web interface to find configuration options")
    print("=" * 60)
    
    explorer = DeviceExplorer()
    
    # Login
    if not explorer.login():
        print(f"❌ Cannot login to device")
        return
    
    # Start exploration from main page
    print(f"\n🔍 Starting interface exploration...")
    explorer.explore_page(explorer.base_url)
    
    # Print summary
    explorer.print_summary()
    
    print(f"\n📋 NEXT STEPS:")
    print("1. Check the 'MOST PROMISING CONFIGURATION PAGES' above")
    print("2. Manually visit these URLs in your browser")
    print("3. Look for TCP/IP, Communication, or Network settings")
    print("4. Configure Device ID: 181, Common Key: 1302, Port: 32150")

if __name__ == '__main__':
    main()
