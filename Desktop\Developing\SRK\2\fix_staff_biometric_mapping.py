#!/usr/bin/env python3
"""
Script to fix staff ID mapping between database and biometric device
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from zk_biometric import ZKBiometricDevice
import sqlite3

def get_biometric_users():
    """Get all users from biometric device"""
    device_ip = '*************'
    
    try:
        zk_device = ZKBiometricDevice(device_ip)
        if zk_device.connect():
            print("✅ Connected to biometric device")
            
            # Get users from device
            users = zk_device.get_users()
            zk_device.disconnect()
            
            return users
        else:
            print("❌ Failed to connect to biometric device")
            return []
            
    except Exception as e:
        print(f"❌ Error getting biometric users: {e}")
        return []

def get_database_staff():
    """Get all staff from database"""
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        cursor.execute('SELECT id, staff_id, full_name, school_id FROM staff')
        staff_records = cursor.fetchall()
        return staff_records
    except Exception as e:
        print(f"❌ Error getting database staff: {e}")
        return []
    finally:
        conn.close()

def show_mapping_options():
    """Show current staff and biometric users for manual mapping"""
    
    print("=== Staff ID Mapping Analysis ===\n")
    
    # Get biometric users
    print("1. Getting users from biometric device...")
    biometric_users = get_biometric_users()
    
    if biometric_users:
        print(f"✅ Found {len(biometric_users)} users in biometric device:")
        for user in biometric_users[:10]:  # Show first 10
            print(f"   User ID: {user.get('user_id')}, Name: {user.get('name', 'Unknown')}")
        if len(biometric_users) > 10:
            print(f"   ... and {len(biometric_users) - 10} more users")
    else:
        print("⚠️ No users found in biometric device")
    
    # Get database staff
    print("\n2. Getting staff from database...")
    database_staff = get_database_staff()
    
    if database_staff:
        print(f"✅ Found {len(database_staff)} staff in database:")
        for staff in database_staff:
            print(f"   DB ID: {staff['id']}, Staff ID: {staff['staff_id']}, Name: {staff['full_name']}")
    else:
        print("⚠️ No staff found in database")
    
    # Analysis
    print("\n=== Analysis ===")
    if biometric_users and database_staff:
        # Check for exact matches
        biometric_ids = {str(user.get('user_id')) for user in biometric_users}
        database_ids = {str(staff['staff_id']) for staff in database_staff}
        
        matches = biometric_ids.intersection(database_ids)
        
        if matches:
            print(f"✅ Found {len(matches)} exact ID matches: {matches}")
        else:
            print("❌ No exact ID matches found between database and biometric device")
            print("\nPossible solutions:")
            print("1. Update staff IDs in database to match biometric user IDs")
            print("2. Re-enroll staff in biometric device with correct IDs")
            print("3. Create a mapping table to link different IDs")
    
    return biometric_users, database_staff

def update_staff_id_in_database():
    """Interactive function to update staff ID in database"""
    
    biometric_users, database_staff = show_mapping_options()
    
    if not biometric_users or not database_staff:
        print("❌ Cannot proceed without both biometric users and database staff")
        return
    
    print("\n=== Update Staff ID ===")
    print("This will update the staff ID in the database to match a biometric user ID")
    
    # Show database staff
    print("\nCurrent staff in database:")
    for i, staff in enumerate(database_staff):
        print(f"{i+1}. DB ID: {staff['id']}, Staff ID: {staff['staff_id']}, Name: {staff['full_name']}")
    
    try:
        # Get staff selection
        staff_choice = input(f"\nSelect staff to update (1-{len(database_staff)}): ").strip()
        staff_index = int(staff_choice) - 1
        
        if staff_index < 0 or staff_index >= len(database_staff):
            print("❌ Invalid selection")
            return
        
        selected_staff = database_staff[staff_index]
        
        # Show biometric users
        print(f"\nSelected staff: {selected_staff['full_name']} (Current Staff ID: {selected_staff['staff_id']})")
        print("\nAvailable biometric user IDs:")
        for i, user in enumerate(biometric_users[:20]):  # Show first 20
            print(f"{i+1}. User ID: {user.get('user_id')}, Name: {user.get('name', 'Unknown')}")
        
        # Get new staff ID
        new_staff_id = input("\nEnter the biometric User ID to use as new Staff ID: ").strip()
        
        if not new_staff_id:
            print("❌ No ID entered")
            return
        
        # Confirm update
        confirm = input(f"\nUpdate {selected_staff['full_name']}'s Staff ID from '{selected_staff['staff_id']}' to '{new_staff_id}'? (y/N): ").strip().lower()
        
        if confirm == 'y':
            # Update database
            conn = sqlite3.connect('vishnorex.db')
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    UPDATE staff SET staff_id = ? WHERE id = ?
                ''', (new_staff_id, selected_staff['id']))
                
                conn.commit()
                print(f"✅ Successfully updated {selected_staff['full_name']}'s Staff ID to {new_staff_id}")
                print("Staff can now login using the new Staff ID")
                
            except Exception as e:
                print(f"❌ Error updating database: {e}")
                conn.rollback()
            finally:
                conn.close()
        else:
            print("❌ Update cancelled")
            
    except (ValueError, KeyboardInterrupt):
        print("❌ Invalid input or cancelled")

if __name__ == '__main__':
    print("=== Staff Biometric Mapping Fix ===\n")
    
    print("This script helps fix the mapping between database staff IDs and biometric user IDs")
    print("Choose an option:")
    print("1. Show current mapping analysis")
    print("2. Update staff ID in database to match biometric user ID")
    
    try:
        choice = input("\nEnter choice (1 or 2): ").strip()
        
        if choice == '1':
            show_mapping_options()
        elif choice == '2':
            update_staff_id_in_database()
        else:
            print("❌ Invalid choice")
            
    except KeyboardInterrupt:
        print("\n❌ Cancelled by user")
